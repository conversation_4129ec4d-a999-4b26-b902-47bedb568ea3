const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
let folders = [
  {
    folderId: 123456789,
    title: "我的收藏",
    description: "个人收藏的精彩视频",
    cover: "https://i0.hdslb.com/bfs/archive/placeholder.jpg",
    videoCount: 156,
    validVideoCount: 142,
    invalidVideoCount: 14,
    creator: { name: "测试用户", uid: 12345 },
    monitorEnabled: true,
    monitorStatus: "RUNNING",
    monitorInterval: 60,
    preloadEnabled: false,
    preloadStatus: "STOPPED",
    preloadStrategy: "smart",
    preloadInterval: 60,
    updateTime: new Date(),
    lastMonitorTime: new Date(Date.now() - 5 * 60 * 1000),
    recentFailures: 0
  },
  {
    folderId: 987654321,
    title: "学习资料",
    description: "编程学习相关视频",
    cover: "https://i0.hdslb.com/bfs/archive/placeholder.jpg",
    videoCount: 89,
    validVideoCount: 85,
    invalidVideoCount: 4,
    creator: { name: "学习者", uid: 54321 },
    monitorEnabled: false,
    monitorStatus: "STOPPED",
    monitorInterval: 30,
    preloadEnabled: true,
    preloadStatus: "RUNNING",
    preloadStrategy: "full",
    preloadInterval: 120,
    updateTime: new Date(),
    lastMonitorTime: new Date(Date.now() - 15 * 60 * 1000),
    recentFailures: 1
  }
];

let systemStatus = {
  healthy: true,
  memory: {
    used: 512 * 1024 * 1024,
    free: 1024 * 1024 * 1024,
    max: 2048 * 1024 * 1024
  },
  uptime: Date.now() - 30 * 60 * 1000
};

// API路由
app.get('/api/folders', (req, res) => {
  res.json({
    success: true,
    data: folders,
    total: folders.length
  });
});

app.get('/api/folders/:id', (req, res) => {
  const folder = folders.find(f => f.folderId == req.params.id);
  if (folder) {
    res.json({ success: true, data: folder });
  } else {
    res.status(404).json({ success: false, message: '收藏夹不存在' });
  }
});

app.post('/api/folders', (req, res) => {
  const { folderId } = req.body;
  if (folders.find(f => f.folderId == folderId)) {
    return res.status(400).json({ success: false, message: '收藏夹已存在' });
  }
  
  const newFolder = {
    folderId: parseInt(folderId),
    title: `收藏夹${folderId}`,
    description: "新添加的收藏夹",
    cover: "https://i0.hdslb.com/bfs/archive/placeholder.jpg",
    videoCount: Math.floor(Math.random() * 100) + 10,
    validVideoCount: 0,
    invalidVideoCount: 0,
    creator: { name: "用户", uid: 12345 },
    monitorEnabled: false,
    monitorStatus: "STOPPED",
    monitorInterval: 60,
    preloadEnabled: false,
    preloadStatus: "STOPPED",
    preloadStrategy: "smart",
    preloadInterval: 60,
    updateTime: new Date(),
    lastMonitorTime: null,
    recentFailures: 0
  };
  
  folders.push(newFolder);
  res.json({ success: true, data: newFolder });
});

app.get('/api/system/status', (req, res) => {
  res.json({
    success: true,
    data: systemStatus
  });
});

app.get('/api/system/health', (req, res) => {
  res.json({
    success: true,
    data: { status: 'UP', healthy: true }
  });
});

app.get('/api/monitor/status', (req, res) => {
  const monitoringCount = folders.filter(f => f.monitorStatus === 'RUNNING').length;
  res.json({
    success: true,
    data: {
      enabled: true,
      monitoringCount,
      maxConcurrent: 5,
      monitoringFolders: folders.filter(f => f.monitorStatus === 'RUNNING').map(f => f.folderId)
    }
  });
});

app.post('/api/monitor/:id/start', (req, res) => {
  const folder = folders.find(f => f.folderId == req.params.id);
  if (folder) {
    folder.monitorEnabled = true;
    folder.monitorStatus = 'RUNNING';
    res.json({ success: true, message: '监控已启动' });
    
    // 发送WebSocket消息
    broadcastMessage({
      type: 'monitor_status',
      data: { folderId: folder.folderId, status: 'STARTED' }
    });
  } else {
    res.status(404).json({ success: false, message: '收藏夹不存在' });
  }
});

app.post('/api/monitor/:id/stop', (req, res) => {
  const folder = folders.find(f => f.folderId == req.params.id);
  if (folder) {
    folder.monitorEnabled = false;
    folder.monitorStatus = 'STOPPED';
    res.json({ success: true, message: '监控已停止' });
    
    // 发送WebSocket消息
    broadcastMessage({
      type: 'monitor_status',
      data: { folderId: folder.folderId, status: 'STOPPED' }
    });
  } else {
    res.status(404).json({ success: false, message: '收藏夹不存在' });
  }
});

app.get('/api/preload/status', (req, res) => {
  const preloadingCount = folders.filter(f => f.preloadStatus === 'RUNNING').length;
  res.json({
    success: true,
    data: {
      enabled: true,
      preloadingCount,
      maxConcurrent: 3,
      defaultStrategy: 'smart',
      availableStrategies: ['smart', 'full'],
      preloadingFolders: folders.filter(f => f.preloadStatus === 'RUNNING').map(f => f.folderId)
    }
  });
});

// WebSocket连接处理
wss.on('connection', (ws) => {
  console.log('WebSocket客户端已连接');
  
  // 发送连接确认消息
  ws.send(JSON.stringify({
    type: 'connection',
    message: 'WebSocket连接成功',
    timestamp: new Date()
  }));
  
  // 处理客户端消息
  ws.on('message', (message) => {
    const data = message.toString();
    if (data === 'ping') {
      ws.send(JSON.stringify({
        type: 'pong',
        message: 'pong',
        timestamp: new Date()
      }));
    }
  });
  
  ws.on('close', () => {
    console.log('WebSocket客户端已断开');
  });
});

// 广播消息给所有客户端
function broadcastMessage(message) {
  const data = JSON.stringify({
    ...message,
    timestamp: new Date()
  });
  
  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(data);
    }
  });
}

// 启动服务器
const PORT = 8080;
server.listen(PORT, () => {
  console.log(`🚀 模拟后端服务已启动: http://localhost:${PORT}`);
  console.log(`📡 WebSocket服务: ws://localhost:${PORT}/ws`);
  console.log(`📊 API文档: http://localhost:${PORT}/api`);
});

// 模拟定期数据更新
setInterval(() => {
  // 模拟内存使用变化
  systemStatus.memory.used = Math.floor(Math.random() * 1024 * 1024 * 1024) + 256 * 1024 * 1024;
  
  // 随机发送一些事件
  if (Math.random() < 0.1) {
    broadcastMessage({
      type: 'system_status',
      message: '系统状态更新',
      data: systemStatus
    });
  }
}, 5000);
