# MeBiliDown

🚀 **现代化的B站收藏夹监控与预下载系统**

## 📋 项目简介

MeBiliDown 是一个专注于B站收藏夹监控和视频预下载的现代化系统，采用前后端分离架构，提供流畅的用户体验。

### 🎯 核心功能

- **📊 收藏夹监控**：实时监控收藏夹变化，检测新增/失效视频
- **⬇️ 智能预下载**：多种预下载策略，支持全量/智能模式
- **🎛️ 现代化UI**：基于Vue.js的响应式Web界面
- **🔄 实时更新**：WebSocket实时状态推送
- **⚙️ 灵活配置**：收藏夹级别的个性化配置

## 🏗️ 技术架构

### 后端技术栈
- **框架**：Spring Boot 2.7+
- **构建**：Maven
- **存储**：JSON文件 + 可选H2数据库
- **API**：REST + WebSocket
- **日志**：Logback

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **构建**：Vite（超快热重载）
- **UI库**：Element Plus
- **状态管理**：Pinia
- **HTTP客户端**：Axios

## 🚀 快速开始

### 环境要求
- Java 8+
- Node.js 16+
- Maven 3.6+

### 启动后端
```bash
cd backend
mvn spring-boot:run
```

### 启动前端
```bash
cd frontend
npm install
npm run dev
```

### 访问应用
- 前端界面：http://localhost:3000
- 后端API：http://localhost:8080

## 📊 功能特性

### 🔍 监控功能
- [x] 收藏夹实时监控
- [x] 视频状态变化检测
- [x] 失效视频自动识别
- [x] 新增视频自动发现

### ⬇️ 预下载功能
- [x] 智能预下载策略
- [x] 全量预下载模式
- [x] CDN链接管理
- [x] 过期链接自动刷新

### 🎛️ 界面功能
- [x] 现代化响应式设计
- [x] 实时状态更新
- [x] 可视化数据展示
- [x] 灵活的配置管理

## 📈 开发进度

- [x] 第一阶段：核心功能迁移
- [x] 第二阶段：现代化UI开发
- [ ] 第三阶段：功能增强

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
