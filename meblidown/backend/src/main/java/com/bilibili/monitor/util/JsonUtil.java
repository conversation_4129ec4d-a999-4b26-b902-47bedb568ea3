package com.bilibili.monitor.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class JsonUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonUtil.class);
    
    private ObjectMapper objectMapper;
    
    @PostConstruct
    public void init() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        logger.info("JSON工具类初始化完成");
    }
    
    /**
     * 对象转JSON字符串
     */
    public String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            logger.error("对象转JSON失败", e);
            return null;
        }
    }
    
    /**
     * 对象转格式化JSON字符串
     */
    public String toPrettyJson(Object obj) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            logger.error("对象转格式化JSON失败", e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象
     */
    public <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            logger.error("JSON转对象失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * JSON字符串转对象（泛型）
     */
    public <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            logger.error("JSON转对象失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从文件读取JSON并转换为对象
     */
    public <T> T fromJsonFile(String filePath, Class<T> clazz) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                logger.warn("JSON文件不存在: {}", filePath);
                return null;
            }
            return objectMapper.readValue(file, clazz);
        } catch (IOException e) {
            logger.error("从文件读取JSON失败: {} - {}", filePath, e.getMessage());
            return null;
        }
    }
    
    /**
     * 从文件读取JSON并转换为对象（泛型）
     */
    public <T> T fromJsonFile(String filePath, TypeReference<T> typeReference) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                logger.warn("JSON文件不存在: {}", filePath);
                return null;
            }
            return objectMapper.readValue(file, typeReference);
        } catch (IOException e) {
            logger.error("从文件读取JSON失败: {} - {}", filePath, e.getMessage());
            return null;
        }
    }
    
    /**
     * 将对象保存为JSON文件
     */
    public boolean toJsonFile(Object obj, String filePath) {
        try {
            File file = new File(filePath);
            // 确保父目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, obj);
            logger.debug("JSON文件保存成功: {}", filePath);
            return true;
        } catch (IOException e) {
            logger.error("保存JSON文件失败: {} - {}", filePath, e.getMessage());
            return false;
        }
    }
    
    /**
     * 解析JSON字符串为JsonNode
     */
    public JsonNode parseJson(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JsonNode获取字符串值
     */
    public String getString(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }
    
    /**
     * 从JsonNode获取整数值
     */
    public Integer getInt(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asInt() : null;
    }
    
    /**
     * 从JsonNode获取长整数值
     */
    public Long getLong(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asLong() : null;
    }
    
    /**
     * 从JsonNode获取布尔值
     */
    public Boolean getBoolean(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asBoolean() : null;
    }
    
    /**
     * 检查JSON字符串是否有效
     */
    public boolean isValidJson(String json) {
        try {
            objectMapper.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 检查B站API响应是否成功
     */
    public boolean isBiliApiSuccess(String response) {
        JsonNode root = parseJson(response);
        if (root == null) {
            return false;
        }
        
        Integer code = getInt(root, "code");
        return code != null && code == 0;
    }
    
    /**
     * 获取B站API响应的数据部分
     */
    public JsonNode getBiliApiData(String response) {
        JsonNode root = parseJson(response);
        if (root == null || !isBiliApiSuccess(response)) {
            return null;
        }
        
        return root.get("data");
    }
    
    /**
     * 获取B站API响应的错误信息
     */
    public String getBiliApiMessage(String response) {
        JsonNode root = parseJson(response);
        if (root == null) {
            return "JSON解析失败";
        }
        
        return getString(root, "message");
    }
}
