package com.bilibili.monitor.service;

import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket服务 - 实时推送监控状态和事件
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class WebSocketService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketService.class);
    
    @Autowired
    private JsonUtil jsonUtil;
    
    // 存储WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    /**
     * 添加WebSocket会话
     */
    public void addSession(String sessionId, WebSocketSession session) {
        sessions.put(sessionId, session);
        logger.info("WebSocket会话已连接: {}", sessionId);
        
        // 发送连接成功消息
        sendMessage(sessionId, createMessage("connection", "connected", "WebSocket连接成功"));
    }
    
    /**
     * 移除WebSocket会话
     */
    public void removeSession(String sessionId) {
        sessions.remove(sessionId);
        logger.info("WebSocket会话已断开: {}", sessionId);
    }
    
    /**
     * 向指定会话发送消息
     */
    public void sendMessage(String sessionId, Map<String, Object> message) {
        WebSocketSession session = sessions.get(sessionId);
        if (session != null && session.isOpen()) {
            try {
                String json = jsonUtil.toJson(message);
                session.sendMessage(new TextMessage(json));
                logger.debug("发送WebSocket消息: {} -> {}", sessionId, message.get("type"));
            } catch (Exception e) {
                logger.error("发送WebSocket消息失败: {}", sessionId, e);
            }
        }
    }
    
    /**
     * 广播消息给所有会话
     */
    public void broadcast(Map<String, Object> message) {
        String json = jsonUtil.toJson(message);
        sessions.forEach((sessionId, session) -> {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(json));
                } catch (Exception e) {
                    logger.error("广播消息失败: {}", sessionId, e);
                }
            }
        });
        logger.debug("广播WebSocket消息: {} -> {} 个会话", message.get("type"), sessions.size());
    }
    
    /**
     * 发送监控状态变化
     */
    public void sendMonitorStatus(Long folderId, String status) {
        Map<String, Object> data = new HashMap<>();
        data.put("folderId", folderId);
        data.put("status", status);
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("monitor_status", data, "监控状态变化");
        broadcast(message);
    }
    
    /**
     * 发送新视频通知
     */
    public void sendNewVideos(Long folderId, List<VideoInfo> newVideos) {
        Map<String, Object> data = new HashMap<>();
        data.put("folderId", folderId);
        data.put("videos", newVideos);
        data.put("count", newVideos.size());
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("new_videos", data, 
                String.format("发现 %d 个新视频", newVideos.size()));
        broadcast(message);
    }
    
    /**
     * 发送失效视频通知
     */
    public void sendInvalidVideos(Long folderId, List<VideoInfo> invalidVideos) {
        Map<String, Object> data = new HashMap<>();
        data.put("folderId", folderId);
        data.put("videos", invalidVideos);
        data.put("count", invalidVideos.size());
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("invalid_videos", data, 
                String.format("发现 %d 个失效视频", invalidVideos.size()));
        broadcast(message);
    }
    
    /**
     * 发送预下载状态变化
     */
    public void sendPreloadStatus(Long folderId, String status) {
        Map<String, Object> data = new HashMap<>();
        data.put("folderId", folderId);
        data.put("status", status);
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("preload_status", data, "预下载状态变化");
        broadcast(message);
    }
    
    /**
     * 发送预下载进度
     */
    public void sendPreloadProgress(Long folderId, int completed, int total) {
        Map<String, Object> data = new HashMap<>();
        data.put("folderId", folderId);
        data.put("completed", completed);
        data.put("total", total);
        data.put("progress", total > 0 ? (double) completed / total * 100 : 0);
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("preload_progress", data, 
                String.format("预下载进度: %d/%d", completed, total));
        broadcast(message);
    }
    
    /**
     * 发送系统状态
     */
    public void sendSystemStatus(Map<String, Object> status) {
        Map<String, Object> message = createMessage("system_status", status, "系统状态更新");
        broadcast(message);
    }
    
    /**
     * 发送错误通知
     */
    public void sendError(String error, String details) {
        Map<String, Object> data = new HashMap<>();
        data.put("error", error);
        data.put("details", details);
        data.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> message = createMessage("error", data, error);
        broadcast(message);
    }
    
    /**
     * 发送成功通知
     */
    public void sendSuccess(String message, Object data) {
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("message", message);
        messageData.put("data", data);
        messageData.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> wsMessage = createMessage("success", messageData, message);
        broadcast(wsMessage);
    }
    
    /**
     * 创建标准消息格式
     */
    private Map<String, Object> createMessage(String type, Object data, String message) {
        Map<String, Object> wsMessage = new HashMap<>();
        wsMessage.put("type", type);
        wsMessage.put("data", data);
        wsMessage.put("message", message);
        wsMessage.put("timestamp", System.currentTimeMillis());
        return wsMessage;
    }
    
    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return sessions.size();
    }
    
    /**
     * 获取连接状态
     */
    public Map<String, Object> getConnectionStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("totalConnections", sessions.size());
        status.put("activeConnections", sessions.values().stream()
                .mapToInt(session -> session.isOpen() ? 1 : 0)
                .sum());
        return status;
    }
}
