package com.bilibili.monitor.controller;

import com.bilibili.monitor.dto.ApiResponse;
import com.bilibili.monitor.dto.PageResponse;
import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.service.PreloadService;
import com.bilibili.monitor.service.StorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预下载管理API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/preload")
@CrossOrigin(origins = "*")
public class PreloadController {
    
    private static final Logger logger = LoggerFactory.getLogger(PreloadController.class);
    
    @Autowired
    private PreloadService preloadService;
    
    @Autowired
    private StorageService storageService;
    
    /**
     * 获取预下载状态
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getPreloadStatus() {
        try {
            Map<String, Object> status = preloadService.getPreloadStatus();
            return ApiResponse.success(status);
        } catch (Exception e) {
            logger.error("获取预下载状态失败", e);
            return ApiResponse.error("获取预下载状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动收藏夹预下载
     */
    @PostMapping("/folders/{folderId}/start")
    public ApiResponse<Void> startPreload(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            boolean started = preloadService.startPreload(folderId);
            if (!started) {
                return ApiResponse.error("启动预下载失败，可能已达到最大并发数或预下载已在运行");
            }
            
            logger.info("启动收藏夹预下载成功: {}", folderId);
            return ApiResponse.success("预下载启动成功");
            
        } catch (Exception e) {
            logger.error("启动预下载失败: {}", folderId, e);
            return ApiResponse.error("启动预下载失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止收藏夹预下载
     */
    @PostMapping("/folders/{folderId}/stop")
    public ApiResponse<Void> stopPreload(@PathVariable Long folderId) {
        try {
            boolean stopped = preloadService.stopPreload(folderId);
            if (!stopped) {
                return ApiResponse.error("停止预下载失败");
            }
            
            logger.info("停止收藏夹预下载成功: {}", folderId);
            return ApiResponse.success("预下载停止成功");
            
        } catch (Exception e) {
            logger.error("停止预下载失败: {}", folderId, e);
            return ApiResponse.error("停止预下载失败: " + e.getMessage());
        }
    }
    
    /**
     * 立即执行预下载任务
     */
    @PostMapping("/folders/{folderId}/execute")
    public ApiResponse<Void> executePreload(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            // 异步执行预下载任务
            preloadService.executePreloadTask(folderId);
            
            logger.info("手动执行预下载任务: {}", folderId);
            return ApiResponse.success("预下载任务已开始执行");
            
        } catch (Exception e) {
            logger.error("执行预下载任务失败: {}", folderId, e);
            return ApiResponse.error("执行预下载任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取收藏夹预下载信息（分页）
     */
    @GetMapping("/folders/{folderId}")
    public ApiResponse<PageResponse<PreloadInfo>> getFolderPreloads(
            @PathVariable Long folderId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {
        try {
            List<PreloadInfo> allPreloads = storageService.getFolderPreloads(folderId);
            
            // 根据状态过滤
            if (status != null && !status.isEmpty()) {
                allPreloads = allPreloads.stream()
                        .filter(preload -> {
                            switch (status.toLowerCase()) {
                                case "valid":
                                    return preload.getCdnValid() != null && preload.getCdnValid();
                                case "expired":
                                    return preload.isExpired();
                                case "expiring":
                                    return preload.isExpiringSoon();
                                case "failed":
                                    return preload.getStatus() == PreloadInfo.LinkStatus.FAILED;
                                default:
                                    return true;
                            }
                        })
                        .collect(Collectors.toList());
            }
            
            // 分页处理
            int total = allPreloads.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            
            List<PreloadInfo> pagePreloads = allPreloads.subList(startIndex, endIndex);
            PageResponse<PreloadInfo> pageResponse = PageResponse.of(page, size, total, pagePreloads);
            
            return ApiResponse.success(pageResponse);
            
        } catch (Exception e) {
            logger.error("获取收藏夹预下载信息失败: {}", folderId, e);
            return ApiResponse.error("获取收藏夹预下载信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取预下载统计信息
     */
    @GetMapping("/folders/{folderId}/stats")
    public ApiResponse<Map<String, Object>> getPreloadStats(@PathVariable Long folderId) {
        try {
            List<PreloadInfo> preloads = storageService.getFolderPreloads(folderId);
            
            // 统计各种状态的数量
            long validCount = preloads.stream().filter(p -> p.getCdnValid() != null && p.getCdnValid()).count();
            long expiredCount = preloads.stream().filter(PreloadInfo::isExpired).count();
            long expiringCount = preloads.stream().filter(PreloadInfo::isExpiringSoon).count();
            long failedCount = preloads.stream().filter(p -> p.getStatus() == PreloadInfo.LinkStatus.FAILED).count();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("folderId", folderId);
            stats.put("totalPreloads", preloads.size());
            stats.put("validPreloads", validCount);
            stats.put("expiredPreloads", expiredCount);
            stats.put("expiringPreloads", expiringCount);
            stats.put("failedPreloads", failedCount);
            stats.put("validRate", preloads.size() > 0 ? (double) validCount / preloads.size() * 100 : 0);
            
            return ApiResponse.success(stats);
            
        } catch (Exception e) {
            logger.error("获取预下载统计信息失败: {}", folderId, e);
            return ApiResponse.error("获取预下载统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新过期的CDN链接
     */
    @PostMapping("/refresh-expired")
    public ApiResponse<Void> refreshExpiredLinks() {
        try {
            // 异步执行刷新任务
            preloadService.refreshExpiredLinks();
            
            logger.info("开始刷新过期CDN链接");
            return ApiResponse.success("CDN链接刷新任务已开始执行");
            
        } catch (Exception e) {
            logger.error("刷新CDN链接失败", e);
            return ApiResponse.error("刷新CDN链接失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新预下载配置
     */
    @PutMapping("/folders/{folderId}/config")
    public ApiResponse<FolderInfo> updatePreloadConfig(
            @PathVariable Long folderId,
            @RequestBody Map<String, Object> config) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            // 更新预下载配置
            if (config.containsKey("preloadEnabled")) {
                folder.setPreloadEnabled((Boolean) config.get("preloadEnabled"));
            }
            if (config.containsKey("preloadStrategy")) {
                folder.setPreloadStrategy((String) config.get("preloadStrategy"));
            }
            if (config.containsKey("preloadInterval")) {
                folder.setPreloadInterval((Integer) config.get("preloadInterval"));
            }
            
            // 保存配置
            boolean saved = storageService.saveFolder(folder);
            if (!saved) {
                return ApiResponse.error("保存预下载配置失败");
            }
            
            // 如果预下载已启动，需要重启以应用新配置
            Map<String, Object> preloadStatus = preloadService.getPreloadStatus();
            @SuppressWarnings("unchecked")
            List<Long> preloadingFolders = (List<Long>) preloadStatus.get("preloadingFolders");
            
            if (preloadingFolders.contains(folderId)) {
                preloadService.stopPreload(folderId);
                if (folder.getPreloadEnabled()) {
                    preloadService.startPreload(folderId);
                }
            }
            
            logger.info("更新预下载配置成功: {}", folderId);
            return ApiResponse.success("预下载配置更新成功", folder);
            
        } catch (Exception e) {
            logger.error("更新预下载配置失败: {}", folderId, e);
            return ApiResponse.error("更新预下载配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量启动预下载
     */
    @PostMapping("/start-all")
    public ApiResponse<Map<String, Object>> startAllPreloads() {
        try {
            List<FolderInfo> folders = storageService.getAllFolders();
            int successCount = 0;
            int failCount = 0;
            
            for (FolderInfo folder : folders) {
                if (folder.getPreloadEnabled() != null && folder.getPreloadEnabled()) {
                    boolean started = preloadService.startPreload(folder.getFolderId());
                    if (started) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", folders.size());
            
            logger.info("批量启动预下载完成: 成功{}, 失败{}", successCount, failCount);
            return ApiResponse.success("批量启动预下载完成", result);
            
        } catch (Exception e) {
            logger.error("批量启动预下载失败", e);
            return ApiResponse.error("批量启动预下载失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量停止预下载
     */
    @PostMapping("/stop-all")
    public ApiResponse<Map<String, Object>> stopAllPreloads() {
        try {
            Map<String, Object> status = preloadService.getPreloadStatus();
            @SuppressWarnings("unchecked")
            List<Long> preloadingFolders = (List<Long>) status.get("preloadingFolders");
            
            int successCount = 0;
            int failCount = 0;
            
            for (Long folderId : preloadingFolders) {
                boolean stopped = preloadService.stopPreload(folderId);
                if (stopped) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", preloadingFolders.size());
            
            logger.info("批量停止预下载完成: 成功{}, 失败{}", successCount, failCount);
            return ApiResponse.success("批量停止预下载完成", result);
            
        } catch (Exception e) {
            logger.error("批量停止预下载失败", e);
            return ApiResponse.error("批量停止预下载失败: " + e.getMessage());
        }
    }
}
