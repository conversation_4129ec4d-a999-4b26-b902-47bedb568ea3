package com.bilibili.monitor.strategy;

import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能预下载策略
 * 根据收藏夹大小、视频热度、时间等因素智能选择预下载视频
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class SmartPreloadStrategy implements PreloadStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(SmartPreloadStrategy.class);
    
    // 策略参数
    private static final int BASE_INTERVAL = 60; // 基础间隔（分钟）
    private static final int MIN_INTERVAL = 15;  // 最小间隔（分钟）
    private static final int MAX_INTERVAL = 240; // 最大间隔（分钟）
    
    @Override
    public String getStrategyName() {
        return "smart";
    }
    
    @Override
    public String getDescription() {
        return "智能预下载策略：根据收藏夹大小、视频热度、时间等因素智能选择预下载视频";
    }
    
    @Override
    public List<VideoInfo> selectVideosForPreload(Long folderId, List<VideoInfo> allVideos, List<PreloadInfo> existingPreloads) {
        logger.debug("=== 智能预下载策略开始 ===");
        logger.debug("收藏夹ID: {}, 视频总数: {}, 已预下载: {}", folderId, allVideos.size(), existingPreloads.size());
        
        if (allVideos == null || allVideos.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取已存在预下载的BVID集合
        Set<String> existingBvids = existingPreloads.stream()
                .map(PreloadInfo::getBvid)
                .collect(Collectors.toSet());
        
        // 过滤出候选视频
        List<VideoInfo> candidates = allVideos.stream()
                .filter(video -> video.getValid() != null && video.getValid()) // 只处理有效视频
                .filter(video -> !existingBvids.contains(video.getBvid())) // 排除已预下载的
                .collect(Collectors.toList());
        
        if (candidates.isEmpty()) {
            logger.debug("没有候选视频需要预下载");
            return new ArrayList<>();
        }
        
        // 计算最大预下载数量
        int maxCount = calculateMaxPreloadCount(allVideos.size());
        
        // 为每个候选视频计算优先级
        List<VideoCandidate> videoCandidates = candidates.stream()
                .map(video -> new VideoCandidate(video, calculateVideoPriority(video), getVideoReason(video)))
                .sorted((c1, c2) -> Integer.compare(c2.getPriority(), c1.getPriority())) // 按优先级降序
                .collect(Collectors.toList());
        
        // 选择前N个高优先级视频
        List<VideoInfo> selected = videoCandidates.stream()
                .limit(maxCount)
                .map(VideoCandidate::getVideo)
                .collect(Collectors.toList());
        
        logger.info("智能策略选择完成: 候选{} -> 选中{} (最大{})", candidates.size(), selected.size(), maxCount);
        
        // 输出选择详情
        for (int i = 0; i < Math.min(selected.size(), 5); i++) {
            VideoCandidate candidate = videoCandidates.get(i);
            logger.debug("选中视频{}: {} (优先级:{}, 原因:{})", 
                    i + 1, candidate.getVideo().getBvid(), candidate.getPriority(), candidate.getReason());
        }
        
        return selected;
    }
    
    @Override
    public int calculatePreloadInterval(Long folderId, int folderSize, int recentFailures) {
        int interval = BASE_INTERVAL;
        
        // 根据收藏夹大小调整
        if (folderSize > 1000) {
            interval = (int) (interval * 0.7); // 大收藏夹更频繁
        } else if (folderSize > 500) {
            interval = (int) (interval * 0.8);
        } else if (folderSize < 50) {
            interval = (int) (interval * 1.5); // 小收藏夹不那么频繁
        }
        
        // 根据失败次数调整
        if (recentFailures > 5) {
            interval = (int) (interval * 1.5); // 失败多了降低频率
        } else if (recentFailures > 2) {
            interval = (int) (interval * 1.2);
        } else if (recentFailures == 0) {
            interval = (int) (interval * 0.9); // 没有失败可以更频繁
        }
        
        return Math.max(MIN_INTERVAL, Math.min(MAX_INTERVAL, interval));
    }
    
    @Override
    public int calculatePriority(Long folderId, int folderSize, long lastPreloadTime, int recentFailures) {
        int priority = 0;
        
        // 基于时间的优先级
        long minutesSinceLastPreload = (System.currentTimeMillis() - lastPreloadTime) / (60 * 1000);
        if (minutesSinceLastPreload > 180) {
            priority += 100; // 很久没预下载了
        } else if (minutesSinceLastPreload > 120) {
            priority += 70;
        } else if (minutesSinceLastPreload > 60) {
            priority += 40;
        }
        
        // 基于收藏夹大小的优先级
        if (folderSize > 500) {
            priority += 30; // 大收藏夹优先
        } else if (folderSize > 100) {
            priority += 20;
        }
        
        // 基于失败次数的优先级调整
        if (recentFailures > 3) {
            priority -= 20; // 失败多了降低优先级
        } else if (recentFailures == 0) {
            priority += 10; // 没有失败提高优先级
        }
        
        return Math.max(0, priority);
    }
    
    @Override
    public boolean shouldPreload(Long folderId, int folderSize, long lastPreloadTime, int currentPreloadCount) {
        // 检查并发限制
        if (currentPreloadCount >= 3) { // 最多3个并发预下载任务
            return false;
        }
        
        // 计算优先级
        int priority = calculatePriority(folderId, folderSize, lastPreloadTime, 0);
        
        // 根据优先级和当前负载决定
        if (priority >= 100) {
            return true; // 高优先级立即执行
        } else if (priority >= 50) {
            return currentPreloadCount < 2; // 中优先级在负载较低时执行
        } else if (priority >= 20) {
            return currentPreloadCount == 0; // 低优先级只在空闲时执行
        }
        
        return false;
    }
    
    @Override
    public boolean shouldRefreshLink(PreloadInfo preloadInfo) {
        if (preloadInfo == null) {
            return false;
        }
        
        // 检查CDN链接状态
        if (preloadInfo.getStatus() == PreloadInfo.LinkStatus.FAILED ||
            preloadInfo.getStatus() == PreloadInfo.LinkStatus.EXPIRED) {
            return true;
        }
        
        // 检查剩余时间
        long remainingMinutes = getRemainingMinutes(preloadInfo);
        if (remainingMinutes <= 0) {
            return true; // 已过期
        } else if (remainingMinutes <= 30) {
            return true; // 30分钟内过期
        }
        
        return false;
    }
    
    @Override
    public int calculateRefreshPriority(PreloadInfo preloadInfo) {
        if (preloadInfo == null) {
            return 0;
        }
        
        int priority = 0;
        
        // 基于剩余时间的优先级
        long remainingMinutes = getRemainingMinutes(preloadInfo);
        if (remainingMinutes <= 0) {
            priority += 1000; // 已过期，最高优先级
        } else if (remainingMinutes <= 15) {
            priority += 500; // 即将过期，高优先级
        } else if (remainingMinutes <= 30) {
            priority += 200; // 快过期，中等优先级
        }
        
        // 基于重试次数的优先级调整
        int retryCount = preloadInfo.getRetryCount() != null ? preloadInfo.getRetryCount() : 0;
        if (retryCount == 0) {
            priority += 100; // 首次刷新优先级高
        } else if (retryCount < 3) {
            priority += 50;
        } else {
            priority += 10; // 重试太多次降低优先级
        }
        
        // 基于状态的优先级调整
        switch (preloadInfo.getStatus()) {
            case FAILED:
                priority += 300;
                break;
            case EXPIRED:
                priority += 500;
                break;
            case EXPIRING_SOON:
                priority += 200;
                break;
            default:
                break;
        }
        
        return Math.max(0, priority);
    }
    
    /**
     * 计算最大预下载数量
     */
    protected int calculateMaxPreloadCount(int folderSize) {
        if (folderSize <= 50) {
            return Math.min(folderSize, 20); // 小收藏夹预下载大部分
        } else if (folderSize <= 200) {
            return Math.min(folderSize / 2, 50); // 中等收藏夹预下载一半
        } else if (folderSize <= 500) {
            return Math.min(folderSize / 3, 80); // 大收藏夹预下载三分之一
        } else {
            return 80; // 超大收藏夹最多80个
        }
    }
    
    /**
     * 计算视频优先级
     */
    private int calculateVideoPriority(VideoInfo video) {
        int priority = 0;
        
        // 基于收藏时间的优先级（越新优先级越高）
        if (video.getFavoriteTime() != null) {
            long daysSinceFavorite = ChronoUnit.DAYS.between(video.getFavoriteTime(), LocalDateTime.now());
            if (daysSinceFavorite <= 1) {
                priority += 100; // 最近收藏的
            } else if (daysSinceFavorite <= 7) {
                priority += 70;
            } else if (daysSinceFavorite <= 30) {
                priority += 40;
            } else {
                priority += 10;
            }
        }
        
        // 基于发布时间的优先级（越新优先级越高）
        if (video.getPublishTime() != null) {
            long daysSincePublish = ChronoUnit.DAYS.between(video.getPublishTime(), LocalDateTime.now());
            if (daysSincePublish <= 7) {
                priority += 50; // 最近发布的
            } else if (daysSincePublish <= 30) {
                priority += 30;
            }
        }
        
        // 基于视频统计的优先级
        if (video.getStats() != null) {
            long playCount = video.getStats().getPlayCount() != null ? video.getStats().getPlayCount() : 0;
            long likeCount = video.getStats().getLikeCount() != null ? video.getStats().getLikeCount() : 0;
            
            if (playCount > 1000000) { // 100万播放以上
                priority += 30;
            } else if (playCount > 100000) { // 10万播放以上
                priority += 20;
            } else if (playCount > 10000) { // 1万播放以上
                priority += 10;
            }
            
            if (likeCount > 10000) {
                priority += 20;
            } else if (likeCount > 1000) {
                priority += 10;
            }
        }
        
        // 基于视频时长的优先级（适中时长优先）
        if (video.getDuration() != null) {
            int duration = video.getDuration();
            if (duration >= 300 && duration <= 3600) { // 5分钟到1小时
                priority += 10;
            } else if (duration > 3600) { // 超过1小时降低优先级
                priority -= 10;
            }
        }
        
        return Math.max(0, priority);
    }
    
    /**
     * 获取视频选择原因
     */
    private String getVideoReason(VideoInfo video) {
        List<String> reasons = new ArrayList<>();
        
        if (video.getFavoriteTime() != null) {
            long daysSinceFavorite = ChronoUnit.DAYS.between(video.getFavoriteTime(), LocalDateTime.now());
            if (daysSinceFavorite <= 1) {
                reasons.add("最近收藏");
            } else if (daysSinceFavorite <= 7) {
                reasons.add("近期收藏");
            }
        }
        
        if (video.getPublishTime() != null) {
            long daysSincePublish = ChronoUnit.DAYS.between(video.getPublishTime(), LocalDateTime.now());
            if (daysSincePublish <= 7) {
                reasons.add("新发布");
            }
        }
        
        if (video.getStats() != null) {
            long playCount = video.getStats().getPlayCount() != null ? video.getStats().getPlayCount() : 0;
            if (playCount > 1000000) {
                reasons.add("高播放量");
            }
        }
        
        return reasons.isEmpty() ? "常规选择" : String.join(",", reasons);
    }
    
    /**
     * 获取CDN链接剩余时间（分钟）
     */
    private long getRemainingMinutes(PreloadInfo preloadInfo) {
        if (preloadInfo.getCdnExpireTime() == null) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(preloadInfo.getCdnExpireTime())) {
            return 0;
        }
        
        return ChronoUnit.MINUTES.between(now, preloadInfo.getCdnExpireTime());
    }
}
