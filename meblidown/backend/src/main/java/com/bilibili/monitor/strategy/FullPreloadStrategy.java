package com.bilibili.monitor.strategy;

import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全量预下载策略
 * 预下载收藏夹中的所有有效视频，无数量限制
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class FullPreloadStrategy implements PreloadStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(FullPreloadStrategy.class);
    
    @Override
    public String getStrategyName() {
        return "full";
    }
    
    @Override
    public String getDescription() {
        return "全量预下载策略：预下载收藏夹中的所有有效视频，无数量限制";
    }
    
    @Override
    public List<VideoInfo> selectVideosForPreload(Long folderId, List<VideoInfo> allVideos, List<PreloadInfo> existingPreloads) {
        logger.debug("=== 全量预下载策略开始 ===");
        logger.debug("收藏夹ID: {}, 视频总数: {}, 已预下载: {}", folderId, allVideos.size(), existingPreloads.size());
        
        if (allVideos == null || allVideos.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取已存在预下载的BVID集合
        Set<String> existingBvids = existingPreloads.stream()
                .map(PreloadInfo::getBvid)
                .collect(Collectors.toSet());
        
        // 选择所有有效且未预下载的视频
        List<VideoInfo> selected = allVideos.stream()
                .filter(video -> video.getValid() != null && video.getValid()) // 只处理有效视频
                .filter(video -> !existingBvids.contains(video.getBvid())) // 排除已预下载的
                .sorted((v1, v2) -> {
                    // 按收藏时间降序排列，优先处理最新收藏的
                    if (v1.getFavoriteTime() == null && v2.getFavoriteTime() == null) return 0;
                    if (v1.getFavoriteTime() == null) return 1;
                    if (v2.getFavoriteTime() == null) return -1;
                    return v2.getFavoriteTime().compareTo(v1.getFavoriteTime());
                })
                .collect(Collectors.toList());
        
        logger.info("全量策略选择完成: 候选{} -> 选中{} (全部有效视频)", 
                allVideos.size() - existingBvids.size(), selected.size());
        
        // 计算覆盖率
        double coverageRate = allVideos.size() > 0 ? 
                (double)(selected.size() + existingBvids.size()) / allVideos.size() * 100 : 0;
        logger.info("预下载覆盖率: {:.1f}% ({}/{})", coverageRate, 
                selected.size() + existingBvids.size(), allVideos.size());
        
        return selected;
    }
    
    @Override
    public int calculatePreloadInterval(Long folderId, int folderSize, int recentFailures) {
        // 全量模式使用较短的间隔，确保及时处理新视频
        int baseInterval = 45; // 基础45分钟
        
        // 根据收藏夹大小调整
        if (folderSize > 2000) {
            baseInterval = 30; // 超大收藏夹更频繁
        } else if (folderSize > 1000) {
            baseInterval = 35;
        } else if (folderSize < 100) {
            baseInterval = 60; // 小收藏夹可以稍微放松
        }
        
        // 根据失败次数调整
        if (recentFailures > 5) {
            baseInterval = (int) (baseInterval * 1.3); // 失败多了降低频率
        } else if (recentFailures == 0) {
            baseInterval = (int) (baseInterval * 0.9); // 没有失败可以更频繁
        }
        
        return Math.max(20, Math.min(120, baseInterval)); // 限制在20-120分钟之间
    }
    
    @Override
    public int calculatePriority(Long folderId, int folderSize, long lastPreloadTime, int recentFailures) {
        int priority = 0;
        
        // 全量模式基础优先级较高
        priority += 50;
        
        // 基于时间的优先级
        long minutesSinceLastPreload = (System.currentTimeMillis() - lastPreloadTime) / (60 * 1000);
        if (minutesSinceLastPreload > 120) {
            priority += 150; // 很久没预下载了，全量模式需要更高优先级
        } else if (minutesSinceLastPreload > 60) {
            priority += 100;
        } else if (minutesSinceLastPreload > 30) {
            priority += 50;
        }
        
        // 基于收藏夹大小的优先级（大收藏夹在全量模式下优先级更高）
        if (folderSize > 1000) {
            priority += 50; // 大收藏夹优先
        } else if (folderSize > 500) {
            priority += 30;
        } else if (folderSize > 200) {
            priority += 20;
        }
        
        // 基于失败次数的优先级调整
        if (recentFailures > 5) {
            priority -= 30; // 失败多了显著降低优先级
        } else if (recentFailures > 2) {
            priority -= 15;
        } else if (recentFailures == 0) {
            priority += 20; // 没有失败提高优先级
        }
        
        return Math.max(0, priority);
    }
    
    @Override
    public boolean shouldPreload(Long folderId, int folderSize, long lastPreloadTime, int currentPreloadCount) {
        // 全量模式允许更高的并发数
        if (currentPreloadCount >= 5) { // 最多5个并发预下载任务
            return false;
        }
        
        // 计算优先级
        int priority = calculatePriority(folderId, folderSize, lastPreloadTime, 0);
        
        // 全量模式的判断标准更宽松
        if (priority >= 150) {
            return true; // 高优先级立即执行
        } else if (priority >= 100) {
            return currentPreloadCount < 4; // 中高优先级在负载不太高时执行
        } else if (priority >= 50) {
            return currentPreloadCount < 3; // 中等优先级在负载较低时执行
        } else if (priority >= 20) {
            return currentPreloadCount < 2; // 低优先级在负载很低时执行
        }
        
        return false;
    }
    
    @Override
    public boolean shouldRefreshLink(PreloadInfo preloadInfo) {
        if (preloadInfo == null) {
            return false;
        }
        
        // 全量模式对链接刷新更积极
        if (preloadInfo.getStatus() == PreloadInfo.LinkStatus.FAILED ||
            preloadInfo.getStatus() == PreloadInfo.LinkStatus.EXPIRED) {
            return true;
        }
        
        // 检查剩余时间（全量模式提前更多时间刷新）
        long remainingMinutes = getRemainingMinutes(preloadInfo);
        if (remainingMinutes <= 0) {
            return true; // 已过期
        } else if (remainingMinutes <= 45) { // 45分钟内过期就刷新（比智能策略更早）
            return true;
        }
        
        return false;
    }
    
    @Override
    public int calculateRefreshPriority(PreloadInfo preloadInfo) {
        if (preloadInfo == null) {
            return 0;
        }
        
        int priority = 0;
        
        // 全量模式基础刷新优先级较高
        priority += 50;
        
        // 基于剩余时间的优先级
        long remainingMinutes = getRemainingMinutes(preloadInfo);
        if (remainingMinutes <= 0) {
            priority += 1000; // 已过期，最高优先级
        } else if (remainingMinutes <= 15) {
            priority += 600; // 即将过期，很高优先级
        } else if (remainingMinutes <= 30) {
            priority += 400; // 快过期，高优先级
        } else if (remainingMinutes <= 45) {
            priority += 200; // 全量模式特有：45分钟内也给予较高优先级
        }
        
        // 基于重试次数的优先级调整
        int retryCount = preloadInfo.getRetryCount() != null ? preloadInfo.getRetryCount() : 0;
        if (retryCount == 0) {
            priority += 150; // 首次刷新优先级很高
        } else if (retryCount < 2) {
            priority += 100;
        } else if (retryCount < 4) {
            priority += 50;
        } else {
            priority += 20; // 重试太多次适当降低优先级
        }
        
        // 基于状态的优先级调整
        switch (preloadInfo.getStatus()) {
            case FAILED:
                priority += 400;
                break;
            case EXPIRED:
                priority += 600;
                break;
            case EXPIRING_SOON:
                priority += 300;
                break;
            default:
                break;
        }
        
        return Math.max(0, priority);
    }
    
    /**
     * 获取CDN链接剩余时间（分钟）
     */
    private long getRemainingMinutes(PreloadInfo preloadInfo) {
        if (preloadInfo.getCdnExpireTime() == null) {
            return 0;
        }
        
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        if (now.isAfter(preloadInfo.getCdnExpireTime())) {
            return 0;
        }
        
        return java.time.temporal.ChronoUnit.MINUTES.between(now, preloadInfo.getCdnExpireTime());
    }
}
