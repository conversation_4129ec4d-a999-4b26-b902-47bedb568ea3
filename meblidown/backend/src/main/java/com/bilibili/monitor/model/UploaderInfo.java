package com.bilibili.monitor.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * UP主信息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploaderInfo {
    
    /** UP主用户ID */
    private Long mid;
    
    /** UP主昵称 */
    private String name;
    
    /** UP主头像URL */
    private String face;
    
    /** UP主等级 */
    private Integer level;
    
    /** 是否认证 */
    private Boolean verified = false;
    
    // 构造函数
    public UploaderInfo() {}
    
    public UploaderInfo(Long mid, String name) {
        this.mid = mid;
        this.name = name;
    }
    
    public UploaderInfo(Long mid, String name, String face) {
        this.mid = mid;
        this.name = name;
        this.face = face;
    }
    
    // Getters and Setters
    public Long getMid() { return mid; }
    public void setMid(Long mid) { this.mid = mid; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getFace() { return face; }
    public void setFace(String face) { this.face = face; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public Boolean getVerified() { return verified; }
    public void setVerified(Boolean verified) { this.verified = verified; }
    
    @Override
    public String toString() {
        return "UploaderInfo{" +
                "mid=" + mid +
                ", name='" + name + '\'' +
                ", verified=" + verified +
                '}';
    }
}
