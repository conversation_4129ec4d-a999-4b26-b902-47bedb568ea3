package com.bilibili.monitor.config;

import com.bilibili.monitor.service.PreloadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class ScheduleConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ScheduleConfig.class);
    
    @Autowired
    private PreloadService preloadService;
    
    /**
     * 定时刷新过期的CDN链接
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟
    public void refreshExpiredCdnLinks() {
        try {
            logger.debug("开始定时刷新过期CDN链接");
            preloadService.refreshExpiredLinks();
        } catch (Exception e) {
            logger.error("定时刷新CDN链接失败", e);
        }
    }
    
    /**
     * 定时清理系统资源
     * 每2小时执行一次
     */
    @Scheduled(fixedRate = 2 * 60 * 60 * 1000) // 2小时
    public void cleanupSystemResources() {
        try {
            logger.debug("开始定时清理系统资源");
            
            // 触发垃圾回收
            System.gc();
            
            logger.debug("系统资源清理完成");
        } catch (Exception e) {
            logger.error("系统资源清理失败", e);
        }
    }
}
