package com.bilibili.monitor.handler;

import com.bilibili.monitor.service.WebSocketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

/**
 * 系统WebSocket处理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class SystemWebSocketHandler implements WebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemWebSocketHandler.class);
    
    @Autowired
    private WebSocketService webSocketService;
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        webSocketService.addSession(sessionId, session);
        logger.info("WebSocket连接建立: {}", sessionId);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();
        logger.debug("收到WebSocket消息: {} -> {}", sessionId, payload);
        
        // 这里可以处理客户端发送的消息
        // 例如：心跳检测、订阅特定事件等
        if ("ping".equals(payload)) {
            session.sendMessage(new TextMessage("pong"));
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        logger.error("WebSocket传输错误: {}", sessionId, exception);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        webSocketService.removeSession(sessionId);
        logger.info("WebSocket连接关闭: {} - {}", sessionId, closeStatus);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
