package com.bilibili.monitor.strategy;

import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;

import java.util.List;

/**
 * 预下载策略接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface PreloadStrategy {
    
    /**
     * 获取策略名称
     */
    String getStrategyName();
    
    /**
     * 获取策略描述
     */
    String getDescription();
    
    /**
     * 选择需要预下载的视频
     * 
     * @param folderId 收藏夹ID
     * @param allVideos 所有视频列表
     * @param existingPreloads 已存在的预下载信息
     * @return 需要预下载的视频列表
     */
    List<VideoInfo> selectVideosForPreload(Long folderId, List<VideoInfo> allVideos, List<PreloadInfo> existingPreloads);
    
    /**
     * 计算预下载间隔（分钟）
     * 
     * @param folderId 收藏夹ID
     * @param folderSize 收藏夹大小
     * @param recentFailures 最近失败次数
     * @return 预下载间隔（分钟）
     */
    int calculatePreloadInterval(Long folderId, int folderSize, int recentFailures);
    
    /**
     * 计算预下载优先级
     * 
     * @param folderId 收藏夹ID
     * @param folderSize 收藏夹大小
     * @param lastPreloadTime 上次预下载时间
     * @param recentFailures 最近失败次数
     * @return 优先级（数值越大优先级越高）
     */
    int calculatePriority(Long folderId, int folderSize, long lastPreloadTime, int recentFailures);
    
    /**
     * 判断是否应该执行预下载
     * 
     * @param folderId 收藏夹ID
     * @param folderSize 收藏夹大小
     * @param lastPreloadTime 上次预下载时间
     * @param currentPreloadCount 当前预下载任务数
     * @return 是否应该预下载
     */
    boolean shouldPreload(Long folderId, int folderSize, long lastPreloadTime, int currentPreloadCount);
    
    /**
     * 判断是否需要刷新CDN链接
     * 
     * @param preloadInfo 预下载信息
     * @return 是否需要刷新
     */
    boolean shouldRefreshLink(PreloadInfo preloadInfo);
    
    /**
     * 计算刷新优先级
     * 
     * @param preloadInfo 预下载信息
     * @return 刷新优先级
     */
    int calculateRefreshPriority(PreloadInfo preloadInfo);
    
    /**
     * 视频信息包装类（用于策略内部处理）
     */
    class VideoCandidate {
        private final VideoInfo video;
        private final int priority;
        private final String reason;
        
        public VideoCandidate(VideoInfo video, int priority, String reason) {
            this.video = video;
            this.priority = priority;
            this.reason = reason;
        }
        
        public VideoInfo getVideo() { return video; }
        public int getPriority() { return priority; }
        public String getReason() { return reason; }
        
        @Override
        public String toString() {
            return String.format("VideoCandidate{bvid='%s', priority=%d, reason='%s'}", 
                    video.getBvid(), priority, reason);
        }
    }
}
