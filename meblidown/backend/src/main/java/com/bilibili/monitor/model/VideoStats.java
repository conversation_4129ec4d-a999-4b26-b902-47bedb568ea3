package com.bilibili.monitor.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 视频统计信息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoStats {
    
    /** 播放量 */
    private Long playCount = 0L;
    
    /** 弹幕数 */
    private Long danmakuCount = 0L;
    
    /** 评论数 */
    private Long replyCount = 0L;
    
    /** 收藏数 */
    private Long favoriteCount = 0L;
    
    /** 投币数 */
    private Long coinCount = 0L;
    
    /** 分享数 */
    private Long shareCount = 0L;
    
    /** 点赞数 */
    private Long likeCount = 0L;
    
    // 构造函数
    public VideoStats() {}
    
    public VideoStats(Long playCount, Long danmakuCount, Long replyCount, Long favoriteCount) {
        this.playCount = playCount;
        this.danmakuCount = danmakuCount;
        this.replyCount = replyCount;
        this.favoriteCount = favoriteCount;
    }
    
    // Getters and Setters
    public Long getPlayCount() { return playCount; }
    public void setPlayCount(Long playCount) { this.playCount = playCount; }
    
    public Long getDanmakuCount() { return danmakuCount; }
    public void setDanmakuCount(Long danmakuCount) { this.danmakuCount = danmakuCount; }
    
    public Long getReplyCount() { return replyCount; }
    public void setReplyCount(Long replyCount) { this.replyCount = replyCount; }
    
    public Long getFavoriteCount() { return favoriteCount; }
    public void setFavoriteCount(Long favoriteCount) { this.favoriteCount = favoriteCount; }
    
    public Long getCoinCount() { return coinCount; }
    public void setCoinCount(Long coinCount) { this.coinCount = coinCount; }
    
    public Long getShareCount() { return shareCount; }
    public void setShareCount(Long shareCount) { this.shareCount = shareCount; }
    
    public Long getLikeCount() { return likeCount; }
    public void setLikeCount(Long likeCount) { this.likeCount = likeCount; }
    
    /**
     * 获取总互动数
     */
    public Long getTotalInteraction() {
        return (likeCount != null ? likeCount : 0) +
               (coinCount != null ? coinCount : 0) +
               (favoriteCount != null ? favoriteCount : 0) +
               (shareCount != null ? shareCount : 0);
    }
    
    @Override
    public String toString() {
        return "VideoStats{" +
                "play=" + playCount +
                ", danmaku=" + danmakuCount +
                ", reply=" + replyCount +
                ", favorite=" + favoriteCount +
                ", like=" + likeCount +
                '}';
    }
}
