package com.bilibili.monitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * MeBiliDown 主应用程序
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class MeBiliDownApplication {

    public static void main(String[] args) {
        System.out.println("🚀 启动 MeBiliDown - 现代化B站监控与预下载系统");
        SpringApplication.run(MeBiliDownApplication.class, args);
        System.out.println("✅ MeBiliDown 启动完成！");
        System.out.println("📊 监控面板: http://localhost:8080");
        System.out.println("📡 API文档: http://localhost:8080/actuator");
    }
}
