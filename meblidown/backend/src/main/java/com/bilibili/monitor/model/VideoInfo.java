package com.bilibili.monitor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;

/**
 * 视频信息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoInfo {
    
    /** 视频BV号 */
    private String bvid;
    
    /** 视频AV号 */
    private String aid;
    
    /** 视频标题 */
    private String title;
    
    /** 视频简介 */
    private String description;
    
    /** 视频封面URL */
    private String cover;
    
    /** 视频时长（秒） */
    private Integer duration;
    
    /** UP主信息 */
    private UploaderInfo uploader;
    
    /** 视频统计信息 */
    private VideoStats stats;
    
    /** 视频发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;
    
    /** 收藏时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime favoriteTime;
    
    /** 视频状态 */
    private VideoStatus status = VideoStatus.VALID;
    
    /** 所属收藏夹ID */
    private Long folderId;
    
    /** 视频CID */
    private Long cid;
    
    /** 视频质量 */
    private Integer quality;
    
    /** 是否有效 */
    private Boolean valid = true;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime = LocalDateTime.now();
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime = LocalDateTime.now();
    
    // 构造函数
    public VideoInfo() {}
    
    public VideoInfo(String bvid, String aid, String title) {
        this.bvid = bvid;
        this.aid = aid;
        this.title = title;
    }
    
    // Getters and Setters
    public String getBvid() { return bvid; }
    public void setBvid(String bvid) { this.bvid = bvid; }
    
    public String getAid() { return aid; }
    public void setAid(String aid) { this.aid = aid; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getCover() { return cover; }
    public void setCover(String cover) { this.cover = cover; }
    
    public Integer getDuration() { return duration; }
    public void setDuration(Integer duration) { this.duration = duration; }
    
    public UploaderInfo getUploader() { return uploader; }
    public void setUploader(UploaderInfo uploader) { this.uploader = uploader; }
    
    public VideoStats getStats() { return stats; }
    public void setStats(VideoStats stats) { this.stats = stats; }
    
    public LocalDateTime getPublishTime() { return publishTime; }
    public void setPublishTime(LocalDateTime publishTime) { this.publishTime = publishTime; }
    
    public LocalDateTime getFavoriteTime() { return favoriteTime; }
    public void setFavoriteTime(LocalDateTime favoriteTime) { this.favoriteTime = favoriteTime; }
    
    public VideoStatus getStatus() { return status; }
    public void setStatus(VideoStatus status) { this.status = status; }
    
    public Long getFolderId() { return folderId; }
    public void setFolderId(Long folderId) { this.folderId = folderId; }
    
    public Long getCid() { return cid; }
    public void setCid(Long cid) { this.cid = cid; }
    
    public Integer getQuality() { return quality; }
    public void setQuality(Integer quality) { this.quality = quality; }
    
    public Boolean getValid() { return valid; }
    public void setValid(Boolean valid) { this.valid = valid; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    /**
     * 视频状态枚举
     */
    public enum VideoStatus {
        VALID("有效"),
        INVALID("失效"),
        DELETED("已删除"),
        PRIVATE("私有"),
        PROCESSING("处理中");
        
        private final String description;
        
        VideoStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    @Override
    public String toString() {
        return "VideoInfo{" +
                "bvid='" + bvid + '\'' +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", valid=" + valid +
                '}';
    }
}
