package com.bilibili.monitor.dto;

import java.util.List;

/**
 * 分页响应数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PageResponse<T> {
    
    /** 当前页码 */
    private int page;
    
    /** 每页大小 */
    private int size;
    
    /** 总记录数 */
    private long total;
    
    /** 总页数 */
    private int totalPages;
    
    /** 数据列表 */
    private List<T> items;
    
    /** 是否有下一页 */
    private boolean hasNext;
    
    /** 是否有上一页 */
    private boolean hasPrevious;
    
    // 构造函数
    public PageResponse() {}
    
    public PageResponse(int page, int size, long total, List<T> items) {
        this.page = page;
        this.size = size;
        this.total = total;
        this.items = items;
        this.totalPages = (int) Math.ceil((double) total / size);
        this.hasNext = page < totalPages;
        this.hasPrevious = page > 1;
    }
    
    /**
     * 创建分页响应
     */
    public static <T> PageResponse<T> of(int page, int size, long total, List<T> items) {
        return new PageResponse<>(page, size, total, items);
    }
    
    /**
     * 创建空分页响应
     */
    public static <T> PageResponse<T> empty(int page, int size) {
        return new PageResponse<>(page, size, 0, new java.util.ArrayList<>());
    }
    
    // Getters and Setters
    public int getPage() { return page; }
    public void setPage(int page) { this.page = page; }
    
    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }
    
    public long getTotal() { return total; }
    public void setTotal(long total) { this.total = total; }
    
    public int getTotalPages() { return totalPages; }
    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
    
    public List<T> getItems() { return items; }
    public void setItems(List<T> items) { this.items = items; }
    
    public boolean isHasNext() { return hasNext; }
    public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }
    
    public boolean isHasPrevious() { return hasPrevious; }
    public void setHasPrevious(boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    
    @Override
    public String toString() {
        return "PageResponse{" +
                "page=" + page +
                ", size=" + size +
                ", total=" + total +
                ", totalPages=" + totalPages +
                ", itemCount=" + (items != null ? items.size() : 0) +
                '}';
    }
}
