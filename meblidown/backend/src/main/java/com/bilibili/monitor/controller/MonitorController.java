package com.bilibili.monitor.controller;

import com.bilibili.monitor.dto.ApiResponse;
import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.service.MonitorService;
import com.bilibili.monitor.service.StorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控管理API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/monitor")
@CrossOrigin(origins = "*")
public class MonitorController {
    
    private static final Logger logger = LoggerFactory.getLogger(MonitorController.class);
    
    @Autowired
    private MonitorService monitorService;
    
    @Autowired
    private StorageService storageService;
    
    /**
     * 获取监控状态
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getMonitorStatus() {
        try {
            Map<String, Object> status = monitorService.getMonitorStatus();
            return ApiResponse.success(status);
        } catch (Exception e) {
            logger.error("获取监控状态失败", e);
            return ApiResponse.error("获取监控状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动收藏夹监控
     */
    @PostMapping("/folders/{folderId}/start")
    public ApiResponse<Void> startMonitor(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            boolean started = monitorService.startMonitor(folderId);
            if (!started) {
                return ApiResponse.error("启动监控失败，可能已达到最大并发数或监控已在运行");
            }
            
            logger.info("启动收藏夹监控成功: {}", folderId);
            return ApiResponse.success("监控启动成功");
            
        } catch (Exception e) {
            logger.error("启动监控失败: {}", folderId, e);
            return ApiResponse.error("启动监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止收藏夹监控
     */
    @PostMapping("/folders/{folderId}/stop")
    public ApiResponse<Void> stopMonitor(@PathVariable Long folderId) {
        try {
            boolean stopped = monitorService.stopMonitor(folderId);
            if (!stopped) {
                return ApiResponse.error("停止监控失败");
            }
            
            logger.info("停止收藏夹监控成功: {}", folderId);
            return ApiResponse.success("监控停止成功");
            
        } catch (Exception e) {
            logger.error("停止监控失败: {}", folderId, e);
            return ApiResponse.error("停止监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 立即执行监控任务
     */
    @PostMapping("/folders/{folderId}/execute")
    public ApiResponse<Void> executeMonitor(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            // 异步执行监控任务
            monitorService.executeMonitorTask(folderId);
            
            logger.info("手动执行监控任务: {}", folderId);
            return ApiResponse.success("监控任务已开始执行");
            
        } catch (Exception e) {
            logger.error("执行监控任务失败: {}", folderId, e);
            return ApiResponse.error("执行监控任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量启动监控
     */
    @PostMapping("/start-all")
    public ApiResponse<Map<String, Object>> startAllMonitors() {
        try {
            List<FolderInfo> folders = storageService.getAllFolders();
            int successCount = 0;
            int failCount = 0;
            
            for (FolderInfo folder : folders) {
                if (folder.getMonitorEnabled() != null && folder.getMonitorEnabled()) {
                    boolean started = monitorService.startMonitor(folder.getFolderId());
                    if (started) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", folders.size());
            
            logger.info("批量启动监控完成: 成功{}, 失败{}", successCount, failCount);
            return ApiResponse.success("批量启动监控完成", result);
            
        } catch (Exception e) {
            logger.error("批量启动监控失败", e);
            return ApiResponse.error("批量启动监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量停止监控
     */
    @PostMapping("/stop-all")
    public ApiResponse<Map<String, Object>> stopAllMonitors() {
        try {
            Map<String, Object> status = monitorService.getMonitorStatus();
            @SuppressWarnings("unchecked")
            java.util.List<Long> monitoringFolders = (java.util.List<Long>) status.get("monitoringFolders");
            
            int successCount = 0;
            int failCount = 0;
            
            for (Long folderId : monitoringFolders) {
                boolean stopped = monitorService.stopMonitor(folderId);
                if (stopped) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", monitoringFolders.size());
            
            logger.info("批量停止监控完成: 成功{}, 失败{}", successCount, failCount);
            return ApiResponse.success("批量停止监控完成", result);
            
        } catch (Exception e) {
            logger.error("批量停止监控失败", e);
            return ApiResponse.error("批量停止监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新监控配置
     */
    @PutMapping("/folders/{folderId}/config")
    public ApiResponse<FolderInfo> updateMonitorConfig(
            @PathVariable Long folderId,
            @RequestBody Map<String, Object> config) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            // 更新监控配置
            if (config.containsKey("monitorEnabled")) {
                folder.setMonitorEnabled((Boolean) config.get("monitorEnabled"));
            }
            if (config.containsKey("monitorInterval")) {
                folder.setMonitorInterval((Integer) config.get("monitorInterval"));
            }
            
            // 保存配置
            boolean saved = storageService.saveFolder(folder);
            if (!saved) {
                return ApiResponse.error("保存监控配置失败");
            }
            
            // 如果监控已启动，需要重启以应用新配置
            Map<String, Object> monitorStatus = monitorService.getMonitorStatus();
            @SuppressWarnings("unchecked")
            java.util.List<Long> monitoringFolders = (java.util.List<Long>) monitorStatus.get("monitoringFolders");
            
            if (monitoringFolders.contains(folderId)) {
                monitorService.stopMonitor(folderId);
                if (folder.getMonitorEnabled()) {
                    monitorService.startMonitor(folderId);
                }
            }
            
            logger.info("更新监控配置成功: {}", folderId);
            return ApiResponse.success("监控配置更新成功", folder);
            
        } catch (Exception e) {
            logger.error("更新监控配置失败: {}", folderId, e);
            return ApiResponse.error("更新监控配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控日志（最近的监控活动）
     */
    @GetMapping("/logs")
    public ApiResponse<Map<String, Object>> getMonitorLogs(
            @RequestParam(defaultValue = "50") int limit) {
        try {
            // 这里可以实现监控日志的获取
            // 暂时返回基本信息
            Map<String, Object> logs = new HashMap<>();
            logs.put("message", "监控日志功能待实现");
            logs.put("limit", limit);
            
            return ApiResponse.success(logs);
            
        } catch (Exception e) {
            logger.error("获取监控日志失败", e);
            return ApiResponse.error("获取监控日志失败: " + e.getMessage());
        }
    }
}
