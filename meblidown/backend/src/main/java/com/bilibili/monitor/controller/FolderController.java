package com.bilibili.monitor.controller;

import com.bilibili.monitor.dto.ApiResponse;
import com.bilibili.monitor.dto.PageResponse;
import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.service.BilibiliApiService;
import com.bilibili.monitor.service.StorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 收藏夹管理API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/folders")
@CrossOrigin(origins = "*")
public class FolderController {
    
    private static final Logger logger = LoggerFactory.getLogger(FolderController.class);
    
    @Autowired
    private StorageService storageService;
    
    @Autowired
    private BilibiliApiService bilibiliApiService;
    
    /**
     * 获取所有收藏夹列表
     */
    @GetMapping
    public ApiResponse<List<FolderInfo>> getAllFolders() {
        try {
            List<FolderInfo> folders = storageService.getAllFolders();
            logger.info("获取收藏夹列表成功: {} 个", folders.size());
            return ApiResponse.success(folders);
        } catch (Exception e) {
            logger.error("获取收藏夹列表失败", e);
            return ApiResponse.error("获取收藏夹列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取收藏夹信息
     */
    @GetMapping("/{folderId}")
    public ApiResponse<FolderInfo> getFolder(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            return ApiResponse.success(folder);
        } catch (Exception e) {
            logger.error("获取收藏夹信息失败: {}", folderId, e);
            return ApiResponse.error("获取收藏夹信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加收藏夹
     */
    @PostMapping("/{folderId}")
    public ApiResponse<FolderInfo> addFolder(@PathVariable Long folderId) {
        try {
            // 检查收藏夹是否已存在
            FolderInfo existingFolder = storageService.getFolder(folderId);
            if (existingFolder != null) {
                return ApiResponse.badRequest("收藏夹已存在: " + folderId);
            }
            
            // 从B站API获取收藏夹信息
            FolderInfo folder = bilibiliApiService.getFolderInfo(folderId);
            if (folder == null) {
                return ApiResponse.notFound("无法获取收藏夹信息，请检查ID是否正确: " + folderId);
            }
            
            // 保存收藏夹信息
            boolean saved = storageService.saveFolder(folder);
            if (!saved) {
                return ApiResponse.error("保存收藏夹失败");
            }
            
            logger.info("添加收藏夹成功: {} - {}", folderId, folder.getTitle());
            return ApiResponse.success("收藏夹添加成功", folder);
            
        } catch (Exception e) {
            logger.error("添加收藏夹失败: {}", folderId, e);
            return ApiResponse.error("添加收藏夹失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除收藏夹
     */
    @DeleteMapping("/{folderId}")
    public ApiResponse<Void> deleteFolder(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            boolean deleted = storageService.deleteFolder(folderId);
            if (!deleted) {
                return ApiResponse.error("删除收藏夹失败");
            }
            
            logger.info("删除收藏夹成功: {}", folderId);
            return ApiResponse.success("收藏夹删除成功");
            
        } catch (Exception e) {
            logger.error("删除收藏夹失败: {}", folderId, e);
            return ApiResponse.error("删除收藏夹失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新收藏夹配置
     */
    @PutMapping("/{folderId}")
    public ApiResponse<FolderInfo> updateFolder(@PathVariable Long folderId, @RequestBody FolderInfo folderUpdate) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            // 更新可配置的字段
            if (folderUpdate.getMonitorEnabled() != null) {
                folder.setMonitorEnabled(folderUpdate.getMonitorEnabled());
            }
            if (folderUpdate.getMonitorInterval() != null) {
                folder.setMonitorInterval(folderUpdate.getMonitorInterval());
            }
            if (folderUpdate.getPreloadEnabled() != null) {
                folder.setPreloadEnabled(folderUpdate.getPreloadEnabled());
            }
            if (folderUpdate.getPreloadStrategy() != null) {
                folder.setPreloadStrategy(folderUpdate.getPreloadStrategy());
            }
            if (folderUpdate.getPreloadInterval() != null) {
                folder.setPreloadInterval(folderUpdate.getPreloadInterval());
            }
            
            boolean saved = storageService.saveFolder(folder);
            if (!saved) {
                return ApiResponse.error("更新收藏夹配置失败");
            }
            
            logger.info("更新收藏夹配置成功: {}", folderId);
            return ApiResponse.success("收藏夹配置更新成功", folder);
            
        } catch (Exception e) {
            logger.error("更新收藏夹配置失败: {}", folderId, e);
            return ApiResponse.error("更新收藏夹配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取收藏夹视频列表（分页）
     */
    @GetMapping("/{folderId}/videos")
    public ApiResponse<PageResponse<VideoInfo>> getFolderVideos(
            @PathVariable Long folderId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {
        try {
            List<VideoInfo> allVideos = storageService.getFolderVideos(folderId);
            
            // 根据状态过滤
            if (status != null && !status.isEmpty()) {
                allVideos = allVideos.stream()
                        .filter(video -> {
                            switch (status.toLowerCase()) {
                                case "valid":
                                    return video.getValid() != null && video.getValid();
                                case "invalid":
                                    return video.getValid() != null && !video.getValid();
                                default:
                                    return true;
                            }
                        })
                        .collect(Collectors.toList());
            }
            
            // 分页处理
            int total = allVideos.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            
            List<VideoInfo> pageVideos = allVideos.subList(startIndex, endIndex);
            PageResponse<VideoInfo> pageResponse = PageResponse.of(page, size, total, pageVideos);
            
            return ApiResponse.success(pageResponse);
            
        } catch (Exception e) {
            logger.error("获取收藏夹视频列表失败: {}", folderId, e);
            return ApiResponse.error("获取收藏夹视频列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新收藏夹信息
     */
    @PostMapping("/{folderId}/refresh")
    public ApiResponse<FolderInfo> refreshFolder(@PathVariable Long folderId) {
        try {
            // 从B站API获取最新信息
            FolderInfo latestFolder = bilibiliApiService.getFolderInfo(folderId);
            if (latestFolder == null) {
                return ApiResponse.error("无法获取收藏夹最新信息");
            }
            
            // 获取本地收藏夹信息
            FolderInfo localFolder = storageService.getFolder(folderId);
            if (localFolder != null) {
                // 保留本地配置
                latestFolder.setMonitorEnabled(localFolder.getMonitorEnabled());
                latestFolder.setMonitorInterval(localFolder.getMonitorInterval());
                latestFolder.setPreloadEnabled(localFolder.getPreloadEnabled());
                latestFolder.setPreloadStrategy(localFolder.getPreloadStrategy());
                latestFolder.setPreloadInterval(localFolder.getPreloadInterval());
                latestFolder.setLastMonitorTime(localFolder.getLastMonitorTime());
                latestFolder.setLastPreloadTime(localFolder.getLastPreloadTime());
                latestFolder.setRecentFailures(localFolder.getRecentFailures());
                latestFolder.setMonitorStatus(localFolder.getMonitorStatus());
                latestFolder.setPreloadStatus(localFolder.getPreloadStatus());
            }
            
            // 保存更新后的信息
            boolean saved = storageService.saveFolder(latestFolder);
            if (!saved) {
                return ApiResponse.error("保存收藏夹信息失败");
            }
            
            logger.info("刷新收藏夹信息成功: {} - {}", folderId, latestFolder.getTitle());
            return ApiResponse.success("收藏夹信息刷新成功", latestFolder);
            
        } catch (Exception e) {
            logger.error("刷新收藏夹信息失败: {}", folderId, e);
            return ApiResponse.error("刷新收藏夹信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取收藏夹统计信息
     */
    @GetMapping("/{folderId}/stats")
    public ApiResponse<Object> getFolderStats(@PathVariable Long folderId) {
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                return ApiResponse.notFound("收藏夹不存在: " + folderId);
            }
            
            List<VideoInfo> videos = storageService.getFolderVideos(folderId);
            
            // 统计信息
            long validCount = videos.stream().filter(v -> v.getValid() != null && v.getValid()).count();
            long invalidCount = videos.size() - validCount;
            
            // 构建统计数据
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("folderId", folderId);
            stats.put("title", folder.getTitle());
            stats.put("totalVideos", videos.size());
            stats.put("validVideos", validCount);
            stats.put("invalidVideos", invalidCount);
            stats.put("validRate", videos.size() > 0 ? (double) validCount / videos.size() * 100 : 0);
            stats.put("lastMonitorTime", folder.getLastMonitorTime());
            stats.put("lastPreloadTime", folder.getLastPreloadTime());
            stats.put("recentFailures", folder.getRecentFailures());
            stats.put("monitorStatus", folder.getMonitorStatus());
            stats.put("preloadStatus", folder.getPreloadStatus());
            
            return ApiResponse.success(stats);
            
        } catch (Exception e) {
            logger.error("获取收藏夹统计信息失败: {}", folderId, e);
            return ApiResponse.error("获取收藏夹统计信息失败: " + e.getMessage());
        }
    }
}
