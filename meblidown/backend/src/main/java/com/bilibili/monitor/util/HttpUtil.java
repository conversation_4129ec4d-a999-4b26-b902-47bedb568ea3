package com.bilibili.monitor.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP工具类 - 迁移自原项目的HttpRequestUtil
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class HttpUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    
    @Value("${app.bilibili.user-agent}")
    private String userAgent;
    
    @Value("${app.bilibili.timeout:30000}")
    private int timeout;
    
    private CloseableHttpClient httpClient;
    private RequestConfig requestConfig;
    
    @PostConstruct
    public void init() {
        // 创建HTTP客户端配置
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .build();
        
        // 创建HTTP客户端
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        logger.info("HTTP工具类初始化完成，超时时间: {}ms", timeout);
    }
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @return 响应内容
     */
    public String get(String url) {
        return get(url, getDefaultHeaders());
    }
    
    /**
     * 发送GET请求（带请求头）
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应内容
     */
    public String get(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        
        // 设置请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                int statusCode = response.getStatusLine().getStatusCode();
                
                if (statusCode == 200) {
                    logger.debug("GET请求成功: {} ({}字节)", url, content.length());
                    return content;
                } else {
                    logger.warn("GET请求失败: {} - 状态码: {}", url, statusCode);
                    return null;
                }
            }
        } catch (IOException e) {
            logger.error("GET请求异常: {} - {}", url, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 获取B站API的默认请求头
     */
    public Map<String, String> getBiliApiHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", userAgent);
        headers.put("Referer", "https://www.bilibili.com/");
        headers.put("Accept", "application/json, text/plain, */*");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.put("Accept-Encoding", "gzip, deflate, br");
        headers.put("Connection", "keep-alive");
        headers.put("Sec-Fetch-Dest", "empty");
        headers.put("Sec-Fetch-Mode", "cors");
        headers.put("Sec-Fetch-Site", "same-site");
        return headers;
    }
    
    /**
     * 获取B站API的默认请求头（带Cookie）
     */
    public Map<String, String> getBiliApiHeaders(String cookie) {
        Map<String, String> headers = getBiliApiHeaders();
        if (cookie != null && !cookie.trim().isEmpty()) {
            headers.put("Cookie", cookie);
        }
        return headers;
    }
    
    /**
     * 获取默认请求头
     */
    private Map<String, String> getDefaultHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", userAgent);
        return headers;
    }
    
    /**
     * 检查URL是否可访问
     */
    public boolean isUrlAccessible(String url) {
        try {
            String response = get(url);
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            logger.debug("URL不可访问: {} - {}", url, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取B站收藏夹信息
     */
    public String getFolderInfo(Long folderId) {
        String url = String.format("https://api.bilibili.com/x/v3/fav/folder/info?media_id=%d", folderId);
        return get(url, getBiliApiHeaders());
    }
    
    /**
     * 获取B站收藏夹视频列表
     */
    public String getFolderVideos(Long folderId, int page, int pageSize) {
        String url = String.format(
            "https://api.bilibili.com/x/v3/fav/resource/list?media_id=%d&pn=%d&ps=%d&platform=web",
            folderId, page, pageSize
        );
        return get(url, getBiliApiHeaders());
    }
    
    /**
     * 批量获取视频信息
     */
    public String getVideosInfo(String aids) {
        String url = String.format(
            "https://api.bilibili.com/x/v3/fav/resource/infos?resources=%s&platform=web",
            aids
        );
        return get(url, getBiliApiHeaders());
    }
    
    /**
     * 获取视频播放URL
     */
    public String getVideoPlayUrl(String bvid, Long cid, int quality) {
        String url = String.format(
            "https://api.bilibili.com/x/player/wbi/playurl?bvid=%s&cid=%d&qn=%d&platform=pc&high_quality=1",
            bvid, cid, quality
        );
        return get(url, getBiliApiHeaders());
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
                logger.info("HTTP客户端已关闭");
            } catch (IOException e) {
                logger.error("关闭HTTP客户端失败", e);
            }
        }
    }
}
