package com.bilibili.monitor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;

/**
 * 预下载信息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreloadInfo {
    
    /** 视频BV号 */
    private String bvid;
    
    /** 视频AV号 */
    private String aid;
    
    /** 视频标题 */
    private String title;
    
    /** 所属收藏夹ID */
    private Long folderId;
    
    /** 视频CID */
    private Long cid;
    
    /** 视频质量 */
    private Integer quality;
    
    /** 视频下载URL */
    private String videoUrl;
    
    /** 音频下载URL */
    private String audioUrl;
    
    /** CDN链接状态 */
    private LinkStatus status = LinkStatus.PENDING;
    
    /** CDN链接是否有效 */
    private Boolean cdnValid = false;
    
    /** CDN获取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cdnObtainTime;
    
    /** CDN过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cdnExpireTime;
    
    /** 重试次数 */
    private Integer retryCount = 0;
    
    /** 最大重试次数 */
    private Integer maxRetries = 3;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime = LocalDateTime.now();
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime = LocalDateTime.now();
    
    // 构造函数
    public PreloadInfo() {}
    
    public PreloadInfo(String bvid, String aid, String title, Long folderId) {
        this.bvid = bvid;
        this.aid = aid;
        this.title = title;
        this.folderId = folderId;
    }
    
    /**
     * 获取剩余有效时间（分钟）
     */
    public long getRemainingMinutes() {
        if (cdnExpireTime == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(cdnExpireTime)) {
            return 0;
        }
        return java.time.Duration.between(now, cdnExpireTime).toMinutes();
    }
    
    /**
     * 检查CDN链接是否过期
     */
    public boolean isExpired() {
        return getRemainingMinutes() <= 0;
    }
    
    /**
     * 检查CDN链接是否即将过期
     */
    public boolean isExpiringSoon() {
        return getRemainingMinutes() <= 15; // 15分钟内过期
    }
    
    /**
     * 更新状态
     */
    public void updateStatus() {
        if (isExpired()) {
            this.status = LinkStatus.EXPIRED;
            this.cdnValid = false;
        } else if (isExpiringSoon()) {
            this.status = LinkStatus.EXPIRING_SOON;
        } else if (cdnValid && videoUrl != null && !videoUrl.isEmpty()) {
            this.status = LinkStatus.VALID;
        }
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public String getBvid() { return bvid; }
    public void setBvid(String bvid) { this.bvid = bvid; }
    
    public String getAid() { return aid; }
    public void setAid(String aid) { this.aid = aid; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public Long getFolderId() { return folderId; }
    public void setFolderId(Long folderId) { this.folderId = folderId; }
    
    public Long getCid() { return cid; }
    public void setCid(Long cid) { this.cid = cid; }
    
    public Integer getQuality() { return quality; }
    public void setQuality(Integer quality) { this.quality = quality; }
    
    public String getVideoUrl() { return videoUrl; }
    public void setVideoUrl(String videoUrl) { this.videoUrl = videoUrl; }
    
    public String getAudioUrl() { return audioUrl; }
    public void setAudioUrl(String audioUrl) { this.audioUrl = audioUrl; }
    
    public LinkStatus getStatus() { return status; }
    public void setStatus(LinkStatus status) { this.status = status; }
    
    public Boolean getCdnValid() { return cdnValid; }
    public void setCdnValid(Boolean cdnValid) { this.cdnValid = cdnValid; }
    
    public LocalDateTime getCdnObtainTime() { return cdnObtainTime; }
    public void setCdnObtainTime(LocalDateTime cdnObtainTime) { this.cdnObtainTime = cdnObtainTime; }
    
    public LocalDateTime getCdnExpireTime() { return cdnExpireTime; }
    public void setCdnExpireTime(LocalDateTime cdnExpireTime) { this.cdnExpireTime = cdnExpireTime; }
    
    public Integer getRetryCount() { return retryCount; }
    public void setRetryCount(Integer retryCount) { this.retryCount = retryCount; }
    
    public Integer getMaxRetries() { return maxRetries; }
    public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    /**
     * CDN链接状态枚举
     */
    public enum LinkStatus {
        PENDING("等待中"),
        VALID("有效"),
        EXPIRING_SOON("即将过期"),
        EXPIRED("已过期"),
        FAILED("失败"),
        REFRESHING("刷新中");
        
        private final String description;
        
        LinkStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    @Override
    public String toString() {
        return "PreloadInfo{" +
                "bvid='" + bvid + '\'' +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", cdnValid=" + cdnValid +
                ", remainingMinutes=" + getRemainingMinutes() +
                '}';
    }
}
