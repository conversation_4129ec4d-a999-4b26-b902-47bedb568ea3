package com.bilibili.monitor.controller;

import com.bilibili.monitor.dto.ApiResponse;
import com.bilibili.monitor.service.MonitorService;
import com.bilibili.monitor.service.StorageService;
import com.bilibili.monitor.service.WebSocketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统状态API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/system")
@CrossOrigin(origins = "*")
public class SystemController {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemController.class);
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${app.data-path}")
    private String dataPath;
    
    @Autowired
    private StorageService storageService;
    
    @Autowired
    private MonitorService monitorService;
    
    @Autowired
    private WebSocketService webSocketService;
    
    /**
     * 获取系统基本信息
     */
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            
            // 应用信息
            info.put("applicationName", applicationName);
            info.put("version", "1.0.0");
            info.put("dataPath", dataPath);
            info.put("currentTime", LocalDateTime.now());
            
            // JVM信息
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            info.put("jvmName", runtimeBean.getVmName());
            info.put("jvmVersion", runtimeBean.getVmVersion());
            info.put("javaVersion", System.getProperty("java.version"));
            info.put("uptime", runtimeBean.getUptime());
            
            // 操作系统信息
            info.put("osName", System.getProperty("os.name"));
            info.put("osVersion", System.getProperty("os.version"));
            info.put("osArch", System.getProperty("os.arch"));
            
            return ApiResponse.success(info);
            
        } catch (Exception e) {
            logger.error("获取系统信息失败", e);
            return ApiResponse.error("获取系统信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            long freeMemory = maxMemory - usedMemory;
            
            Map<String, Object> memory = new HashMap<>();
            memory.put("used", usedMemory);
            memory.put("free", freeMemory);
            memory.put("max", maxMemory);
            memory.put("usagePercent", (double) usedMemory / maxMemory * 100);
            status.put("memory", memory);
            
            // 存储统计
            Map<String, Object> storageStats = storageService.getStorageStats();
            status.put("storage", storageStats);
            
            // 监控状态
            Map<String, Object> monitorStatus = monitorService.getMonitorStatus();
            status.put("monitor", monitorStatus);
            
            // WebSocket连接状态
            Map<String, Object> wsStatus = webSocketService.getConnectionStatus();
            status.put("websocket", wsStatus);
            
            // 系统健康状态
            status.put("healthy", true);
            status.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.success(status);
            
        } catch (Exception e) {
            logger.error("获取系统状态失败", e);
            return ApiResponse.error("获取系统状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统健康检查
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> getHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 检查各个组件的健康状态
            boolean storageHealthy = checkStorageHealth();
            boolean monitorHealthy = checkMonitorHealth();
            boolean wsHealthy = checkWebSocketHealth();
            
            health.put("storage", storageHealthy);
            health.put("monitor", monitorHealthy);
            health.put("websocket", wsHealthy);
            health.put("overall", storageHealthy && monitorHealthy && wsHealthy);
            health.put("timestamp", LocalDateTime.now());
            
            return ApiResponse.success(health);
            
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ApiResponse.error("健康检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getSystemStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 存储统计
            Map<String, Object> storageStats = storageService.getStorageStats();
            stats.putAll(storageStats);
            
            // 监控统计
            Map<String, Object> monitorStats = monitorService.getMonitorStatus();
            stats.put("monitoringFolders", monitorStats.get("monitoringCount"));
            stats.put("maxConcurrentMonitors", monitorStats.get("maxConcurrent"));
            
            // WebSocket统计
            stats.put("wsConnections", webSocketService.getConnectionCount());
            
            // 运行时统计
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            stats.put("uptime", runtimeBean.getUptime());
            stats.put("startTime", runtimeBean.getStartTime());
            
            return ApiResponse.success(stats);
            
        } catch (Exception e) {
            logger.error("获取系统统计失败", e);
            return ApiResponse.error("获取系统统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 系统配置信息
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 应用配置
            config.put("applicationName", applicationName);
            config.put("dataPath", dataPath);
            
            // 监控配置
            Map<String, Object> monitorStatus = monitorService.getMonitorStatus();
            config.put("monitorEnabled", monitorStatus.get("enabled"));
            config.put("maxConcurrentMonitors", monitorStatus.get("maxConcurrent"));
            
            return ApiResponse.success(config);
            
        } catch (Exception e) {
            logger.error("获取系统配置失败", e);
            return ApiResponse.error("获取系统配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 触发垃圾回收
     */
    @PostMapping("/gc")
    public ApiResponse<Map<String, Object>> triggerGC() {
        try {
            // 记录GC前的内存状态
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long beforeUsed = memoryBean.getHeapMemoryUsage().getUsed();
            
            // 触发垃圾回收
            System.gc();
            
            // 等待一下让GC完成
            Thread.sleep(1000);
            
            // 记录GC后的内存状态
            long afterUsed = memoryBean.getHeapMemoryUsage().getUsed();
            long freed = beforeUsed - afterUsed;
            
            Map<String, Object> result = new HashMap<>();
            result.put("beforeUsed", beforeUsed);
            result.put("afterUsed", afterUsed);
            result.put("freed", freed);
            result.put("timestamp", LocalDateTime.now());
            
            logger.info("手动触发GC完成，释放内存: {} bytes", freed);
            return ApiResponse.success("垃圾回收完成", result);
            
        } catch (Exception e) {
            logger.error("触发垃圾回收失败", e);
            return ApiResponse.error("触发垃圾回收失败: " + e.getMessage());
        }
    }
    
    // 私有方法：检查存储健康状态
    private boolean checkStorageHealth() {
        try {
            storageService.getStorageStats();
            return true;
        } catch (Exception e) {
            logger.warn("存储健康检查失败", e);
            return false;
        }
    }
    
    // 私有方法：检查监控健康状态
    private boolean checkMonitorHealth() {
        try {
            monitorService.getMonitorStatus();
            return true;
        } catch (Exception e) {
            logger.warn("监控健康检查失败", e);
            return false;
        }
    }
    
    // 私有方法：检查WebSocket健康状态
    private boolean checkWebSocketHealth() {
        try {
            webSocketService.getConnectionStatus();
            return true;
        } catch (Exception e) {
            logger.warn("WebSocket健康检查失败", e);
            return false;
        }
    }
}
