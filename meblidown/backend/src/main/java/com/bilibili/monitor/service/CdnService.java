package com.bilibili.monitor.service;

import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.util.HttpUtil;
import com.bilibili.monitor.util.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CDN服务 - 获取B站视频的CDN下载链接
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class CdnService {
    
    private static final Logger logger = LoggerFactory.getLogger(CdnService.class);
    
    @Autowired
    private HttpUtil httpUtil;
    
    @Autowired
    private JsonUtil jsonUtil;
    
    // CDN链接有效期（小时）
    private static final int CDN_EXPIRE_HOURS = 6;
    
    // 支持的视频质量
    private static final int[] QUALITY_LEVELS = {80, 64, 32, 16}; // 1080P, 720P, 480P, 360P
    
    /**
     * 获取视频的CDN下载链接
     */
    public PreloadInfo getCdnLinks(VideoInfo video) {
        if (video == null || video.getBvid() == null || video.getCid() == null) {
            logger.warn("视频信息不完整，无法获取CDN链接: {}", video.getBvid());
            return null;
        }
        
        try {
            logger.debug("获取CDN链接: {} - {}", video.getBvid(), video.getTitle());
            
            // 尝试不同质量等级
            for (int quality : QUALITY_LEVELS) {
                PreloadInfo preloadInfo = getCdnLinksWithQuality(video, quality);
                if (preloadInfo != null) {
                    return preloadInfo;
                }
                
                // 添加延迟，避免请求过于频繁
                Thread.sleep(1000);
            }
            
            logger.warn("所有质量等级都无法获取CDN链接: {}", video.getBvid());
            return null;
            
        } catch (Exception e) {
            logger.error("获取CDN链接异常: {} - {}", video.getBvid(), e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取指定质量的CDN链接
     */
    private PreloadInfo getCdnLinksWithQuality(VideoInfo video, int quality) {
        try {
            String response = httpUtil.getVideoPlayUrl(video.getBvid(), video.getCid(), quality);
            if (response == null || !jsonUtil.isBiliApiSuccess(response)) {
                logger.debug("获取播放URL失败: {} - 质量: {}", video.getBvid(), quality);
                return null;
            }
            
            JsonNode data = jsonUtil.getBiliApiData(response);
            if (data == null) {
                return null;
            }
            
            // 解析视频和音频URL
            CdnResult cdnResult = parseCdnUrls(data);
            if (cdnResult == null || !cdnResult.isValid()) {
                logger.debug("解析CDN链接失败: {} - 质量: {}", video.getBvid(), quality);
                return null;
            }
            
            // 创建预下载信息
            PreloadInfo preloadInfo = new PreloadInfo();
            preloadInfo.setBvid(video.getBvid());
            preloadInfo.setAid(video.getAid());
            preloadInfo.setTitle(video.getTitle());
            preloadInfo.setFolderId(video.getFolderId());
            preloadInfo.setCid(video.getCid());
            preloadInfo.setQuality(quality);
            preloadInfo.setVideoUrl(cdnResult.getVideoUrl());
            preloadInfo.setAudioUrl(cdnResult.getAudioUrl());
            preloadInfo.setCdnObtainTime(LocalDateTime.now());
            preloadInfo.setCdnExpireTime(LocalDateTime.now().plusHours(CDN_EXPIRE_HOURS));
            preloadInfo.setStatus(PreloadInfo.LinkStatus.VALID);
            preloadInfo.setCdnValid(true);
            preloadInfo.setRetryCount(0);
            
            logger.info("获取CDN链接成功: {} - 质量: {}", video.getBvid(), quality);
            return preloadInfo;
            
        } catch (Exception e) {
            logger.debug("获取CDN链接异常: {} - 质量: {} - {}", video.getBvid(), quality, e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析CDN URL
     */
    private CdnResult parseCdnUrls(JsonNode data) {
        try {
            // 获取视频流信息
            JsonNode dashNode = data.get("dash");
            if (dashNode != null) {
                return parseDashUrls(dashNode);
            }
            
            // 获取传统格式信息
            JsonNode durlNode = data.get("durl");
            if (durlNode != null && durlNode.isArray() && durlNode.size() > 0) {
                return parseDurlUrls(durlNode);
            }
            
            logger.warn("未找到有效的视频流信息");
            return null;
            
        } catch (Exception e) {
            logger.error("解析CDN URL异常", e);
            return null;
        }
    }
    
    /**
     * 解析DASH格式的URL
     */
    private CdnResult parseDashUrls(JsonNode dashNode) {
        try {
            String videoUrl = null;
            String audioUrl = null;
            
            // 获取视频URL
            JsonNode videoArray = dashNode.get("video");
            if (videoArray != null && videoArray.isArray() && videoArray.size() > 0) {
                JsonNode firstVideo = videoArray.get(0);
                videoUrl = jsonUtil.getString(firstVideo, "baseUrl");
                if (videoUrl == null) {
                    videoUrl = jsonUtil.getString(firstVideo, "base_url");
                }
            }
            
            // 获取音频URL
            JsonNode audioArray = dashNode.get("audio");
            if (audioArray != null && audioArray.isArray() && audioArray.size() > 0) {
                JsonNode firstAudio = audioArray.get(0);
                audioUrl = jsonUtil.getString(firstAudio, "baseUrl");
                if (audioUrl == null) {
                    audioUrl = jsonUtil.getString(firstAudio, "base_url");
                }
            }
            
            return new CdnResult(videoUrl, audioUrl);
            
        } catch (Exception e) {
            logger.error("解析DASH URL异常", e);
            return null;
        }
    }
    
    /**
     * 解析传统格式的URL
     */
    private CdnResult parseDurlUrls(JsonNode durlNode) {
        try {
            JsonNode firstSegment = durlNode.get(0);
            String url = jsonUtil.getString(firstSegment, "url");
            
            // 传统格式视频和音频在一个文件中
            return new CdnResult(url, null);
            
        } catch (Exception e) {
            logger.error("解析传统URL异常", e);
            return null;
        }
    }
    
    /**
     * 验证CDN链接是否有效
     */
    public boolean validateCdnLink(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 简单的URL格式验证
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                return false;
            }
            
            // 检查是否包含必要的参数
            if (!url.contains("expires=") || !url.contains("auth_key=")) {
                return false;
            }
            
            // 可以添加更多验证逻辑，比如检查过期时间
            return true;
            
        } catch (Exception e) {
            logger.debug("验证CDN链接异常: {} - {}", url, e.getMessage());
            return false;
        }
    }
    
    /**
     * 从URL中提取过期时间
     */
    public LocalDateTime extractExpireTime(String url) {
        if (url == null) {
            return null;
        }
        
        try {
            // 使用正则表达式提取expires参数
            Pattern pattern = Pattern.compile("expires=(\\d+)");
            Matcher matcher = pattern.matcher(url);
            
            if (matcher.find()) {
                long expireTimestamp = Long.parseLong(matcher.group(1));
                return LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochSecond(expireTimestamp),
                        java.time.ZoneId.systemDefault()
                );
            }
            
        } catch (Exception e) {
            logger.debug("提取过期时间失败: {} - {}", url, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * CDN结果内部类
     */
    private static class CdnResult {
        private final String videoUrl;
        private final String audioUrl;
        
        public CdnResult(String videoUrl, String audioUrl) {
            this.videoUrl = videoUrl;
            this.audioUrl = audioUrl;
        }
        
        public String getVideoUrl() { return videoUrl; }
        public String getAudioUrl() { return audioUrl; }
        
        public boolean isValid() {
            return videoUrl != null && !videoUrl.trim().isEmpty();
        }
        
        @Override
        public String toString() {
            return String.format("CdnResult{videoUrl='%s', audioUrl='%s'}", 
                    videoUrl != null ? videoUrl.substring(0, Math.min(50, videoUrl.length())) + "..." : null,
                    audioUrl != null ? audioUrl.substring(0, Math.min(50, audioUrl.length())) + "..." : null);
        }
    }
}
