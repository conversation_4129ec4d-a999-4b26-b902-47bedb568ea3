package com.bilibili.monitor.service;

import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.VideoInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 监控服务 - 迁移自原项目的FavoritesMonitor
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class MonitorService {
    
    private static final Logger logger = LoggerFactory.getLogger(MonitorService.class);
    
    @Value("${app.monitor.enabled:true}")
    private boolean monitorEnabled;
    
    @Value("${app.monitor.default-interval:60}")
    private int defaultInterval;
    
    @Value("${app.monitor.max-concurrent:5}")
    private int maxConcurrent;
    
    @Autowired
    private StorageService storageService;
    
    @Autowired
    private BilibiliApiService bilibiliApiService;
    
    @Autowired
    private WebSocketService webSocketService;
    
    // 监控任务调度器
    private ScheduledExecutorService scheduler;
    
    // 正在监控的收藏夹
    private final Set<Long> monitoringFolders = ConcurrentHashMap.newKeySet();
    
    // 监控任务映射
    private final Map<Long, ScheduledFuture<?>> monitorTasks = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        if (!monitorEnabled) {
            logger.info("监控服务已禁用");
            return;
        }
        
        // 创建调度器
        this.scheduler = Executors.newScheduledThreadPool(maxConcurrent);
        
        // 启动已配置的监控任务
        startConfiguredMonitors();
        
        logger.info("监控服务初始化完成，最大并发: {}", maxConcurrent);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("监控服务已关闭");
    }
    
    /**
     * 启动已配置的监控任务
     */
    private void startConfiguredMonitors() {
        List<FolderInfo> folders = storageService.getAllFolders();
        for (FolderInfo folder : folders) {
            if (folder.getMonitorEnabled() != null && folder.getMonitorEnabled()) {
                startMonitor(folder.getFolderId());
            }
        }
    }
    
    /**
     * 启动收藏夹监控
     */
    public boolean startMonitor(Long folderId) {
        if (!monitorEnabled) {
            logger.warn("监控服务已禁用，无法启动监控: {}", folderId);
            return false;
        }
        
        if (monitoringFolders.contains(folderId)) {
            logger.info("收藏夹已在监控中: {}", folderId);
            return true;
        }
        
        if (monitoringFolders.size() >= maxConcurrent) {
            logger.warn("监控任务已达到最大并发数: {}", maxConcurrent);
            return false;
        }
        
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                // 如果本地没有收藏夹信息，先获取
                folder = bilibiliApiService.getFolderInfo(folderId);
                if (folder == null) {
                    logger.error("无法获取收藏夹信息: {}", folderId);
                    return false;
                }
                storageService.saveFolder(folder);
            }
            
            // 启用监控
            folder.setMonitorEnabled(true);
            folder.setMonitorStatus(FolderInfo.MonitorStatus.RUNNING);
            storageService.saveFolder(folder);
            
            // 获取监控间隔
            int interval = folder.getMonitorInterval() != null ? 
                    folder.getMonitorInterval() : defaultInterval;
            
            // 创建监控任务
            ScheduledFuture<?> task = scheduler.scheduleWithFixedDelay(
                    () -> executeMonitorTask(folderId),
                    0, // 立即开始
                    interval,
                    TimeUnit.MINUTES
            );
            
            monitorTasks.put(folderId, task);
            monitoringFolders.add(folderId);
            
            logger.info("启动收藏夹监控: {} - 间隔: {}分钟", folderId, interval);
            
            // 发送WebSocket通知
            webSocketService.sendMonitorStatus(folderId, "STARTED");
            
            return true;
            
        } catch (Exception e) {
            logger.error("启动监控失败: {}", folderId, e);
            return false;
        }
    }
    
    /**
     * 停止收藏夹监控
     */
    public boolean stopMonitor(Long folderId) {
        try {
            ScheduledFuture<?> task = monitorTasks.remove(folderId);
            if (task != null) {
                task.cancel(false);
            }
            
            monitoringFolders.remove(folderId);
            
            // 更新收藏夹状态
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder != null) {
                folder.setMonitorEnabled(false);
                folder.setMonitorStatus(FolderInfo.MonitorStatus.STOPPED);
                storageService.saveFolder(folder);
            }
            
            logger.info("停止收藏夹监控: {}", folderId);
            
            // 发送WebSocket通知
            webSocketService.sendMonitorStatus(folderId, "STOPPED");
            
            return true;
            
        } catch (Exception e) {
            logger.error("停止监控失败: {}", folderId, e);
            return false;
        }
    }
    
    /**
     * 执行监控任务
     */
    @Async
    public void executeMonitorTask(Long folderId) {
        try {
            logger.debug("开始执行监控任务: {}", folderId);
            
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                logger.error("收藏夹不存在: {}", folderId);
                return;
            }
            
            // 获取当前收藏夹信息
            FolderInfo currentFolder = bilibiliApiService.getFolderInfo(folderId);
            if (currentFolder == null) {
                logger.error("无法获取收藏夹信息: {}", folderId);
                folder.setRecentFailures(folder.getRecentFailures() + 1);
                storageService.saveFolder(folder);
                return;
            }
            
            // 获取当前视频列表
            List<VideoInfo> currentVideos = bilibiliApiService.getAllFolderVideos(folderId);
            List<VideoInfo> existingVideos = storageService.getFolderVideos(folderId);
            
            // 对比分析
            MonitorResult result = compareVideos(existingVideos, currentVideos);
            
            // 更新收藏夹信息
            folder.setTitle(currentFolder.getTitle());
            folder.setDescription(currentFolder.getDescription());
            folder.setCover(currentFolder.getCover());
            folder.setVideoCount(currentVideos.size());
            folder.setLastMonitorTime(LocalDateTime.now());
            folder.setRecentFailures(result.getInvalidVideos().size());
            folder.updateVideoStats();
            
            // 保存新视频
            if (!result.getNewVideos().isEmpty()) {
                storageService.saveVideos(result.getNewVideos());
                logger.info("发现新视频: {} - {} 个", folderId, result.getNewVideos().size());
                
                // 发送WebSocket通知
                webSocketService.sendNewVideos(folderId, result.getNewVideos());
            }
            
            // 更新失效视频
            if (!result.getInvalidVideos().isEmpty()) {
                for (VideoInfo video : result.getInvalidVideos()) {
                    video.setValid(false);
                    video.setStatus(VideoInfo.VideoStatus.INVALID);
                    storageService.saveVideo(video);
                }
                logger.info("发现失效视频: {} - {} 个", folderId, result.getInvalidVideos().size());
                
                // 发送WebSocket通知
                webSocketService.sendInvalidVideos(folderId, result.getInvalidVideos());
            }
            
            // 保存收藏夹信息
            storageService.saveFolder(folder);
            
            logger.info("监控任务完成: {} - 新增:{}, 失效:{}, 总计:{}", 
                    folderId, result.getNewVideos().size(), 
                    result.getInvalidVideos().size(), currentVideos.size());
            
        } catch (Exception e) {
            logger.error("监控任务执行失败: {}", folderId, e);
            
            // 更新失败次数
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder != null) {
                folder.setRecentFailures(folder.getRecentFailures() + 1);
                storageService.saveFolder(folder);
            }
        }
    }
    
    /**
     * 对比视频列表，找出新增和失效的视频
     */
    private MonitorResult compareVideos(List<VideoInfo> existingVideos, List<VideoInfo> currentVideos) {
        Set<String> existingBvids = existingVideos.stream()
                .map(VideoInfo::getBvid)
                .collect(Collectors.toSet());
        
        Set<String> currentBvids = currentVideos.stream()
                .map(VideoInfo::getBvid)
                .collect(Collectors.toSet());
        
        // 找出新增视频
        List<VideoInfo> newVideos = currentVideos.stream()
                .filter(video -> !existingBvids.contains(video.getBvid()))
                .collect(Collectors.toList());
        
        // 找出失效视频
        List<VideoInfo> invalidVideos = existingVideos.stream()
                .filter(video -> !currentBvids.contains(video.getBvid()))
                .filter(video -> video.getValid() == null || video.getValid()) // 只处理之前有效的视频
                .collect(Collectors.toList());
        
        return new MonitorResult(newVideos, invalidVideos);
    }
    
    /**
     * 获取监控状态
     */
    public Map<String, Object> getMonitorStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", monitorEnabled);
        status.put("monitoringCount", monitoringFolders.size());
        status.put("maxConcurrent", maxConcurrent);
        status.put("monitoringFolders", new ArrayList<>(monitoringFolders));
        return status;
    }
    
    /**
     * 监控结果内部类
     */
    private static class MonitorResult {
        private final List<VideoInfo> newVideos;
        private final List<VideoInfo> invalidVideos;
        
        public MonitorResult(List<VideoInfo> newVideos, List<VideoInfo> invalidVideos) {
            this.newVideos = newVideos;
            this.invalidVideos = invalidVideos;
        }
        
        public List<VideoInfo> getNewVideos() { return newVideos; }
        public List<VideoInfo> getInvalidVideos() { return invalidVideos; }
    }
}
