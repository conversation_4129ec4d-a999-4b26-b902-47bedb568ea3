package com.bilibili.monitor.service;

import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.UploaderInfo;
import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.model.VideoStats;
import com.bilibili.monitor.util.HttpUtil;
import com.bilibili.monitor.util.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * B站API服务 - 封装B站API调用
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class BilibiliApiService {
    
    private static final Logger logger = LoggerFactory.getLogger(BilibiliApiService.class);
    
    @Autowired
    private HttpUtil httpUtil;
    
    @Autowired
    private JsonUtil jsonUtil;
    
    /**
     * 获取收藏夹基本信息
     */
    public FolderInfo getFolderInfo(Long folderId) {
        try {
            String response = httpUtil.getFolderInfo(folderId);
            if (response == null || !jsonUtil.isBiliApiSuccess(response)) {
                logger.warn("获取收藏夹信息失败: {}", folderId);
                return null;
            }
            
            JsonNode data = jsonUtil.getBiliApiData(response);
            if (data == null) {
                return null;
            }
            
            FolderInfo folder = new FolderInfo();
            folder.setFolderId(folderId);
            folder.setTitle(jsonUtil.getString(data, "title"));
            folder.setDescription(jsonUtil.getString(data, "intro"));
            folder.setCover(jsonUtil.getString(data, "cover"));
            folder.setVideoCount(jsonUtil.getInt(data, "media_count"));
            
            // 解析创建者信息
            JsonNode upperNode = data.get("upper");
            if (upperNode != null) {
                UploaderInfo creator = new UploaderInfo();
                creator.setMid(jsonUtil.getLong(upperNode, "mid"));
                creator.setName(jsonUtil.getString(upperNode, "name"));
                creator.setFace(jsonUtil.getString(upperNode, "face"));
                folder.setCreator(creator);
            }
            
            // 设置公开状态
            Integer attr = jsonUtil.getInt(data, "attr");
            folder.setIsPublic(attr != null && (attr & 1) == 0); // attr=1表示私有
            
            logger.debug("获取收藏夹信息成功: {} - {}", folderId, folder.getTitle());
            return folder;
            
        } catch (Exception e) {
            logger.error("获取收藏夹信息异常: {}", folderId, e);
            return null;
        }
    }
    
    /**
     * 获取收藏夹视频列表
     */
    public List<VideoInfo> getFolderVideos(Long folderId, int page, int pageSize) {
        try {
            String response = httpUtil.getFolderVideos(folderId, page, pageSize);
            if (response == null || !jsonUtil.isBiliApiSuccess(response)) {
                logger.warn("获取收藏夹视频列表失败: {} - page:{}", folderId, page);
                return new ArrayList<>();
            }
            
            JsonNode data = jsonUtil.getBiliApiData(response);
            if (data == null) {
                return new ArrayList<>();
            }
            
            JsonNode mediasNode = data.get("medias");
            if (mediasNode == null || !mediasNode.isArray()) {
                return new ArrayList<>();
            }
            
            List<VideoInfo> videos = new ArrayList<>();
            for (JsonNode mediaNode : mediasNode) {
                VideoInfo video = parseVideoInfo(mediaNode, folderId);
                if (video != null) {
                    videos.add(video);
                }
            }
            
            logger.debug("获取收藏夹视频列表成功: {} - page:{}, count:{}", folderId, page, videos.size());
            return videos;
            
        } catch (Exception e) {
            logger.error("获取收藏夹视频列表异常: {} - page:{}", folderId, page, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取收藏夹所有视频（自动分页）
     */
    public List<VideoInfo> getAllFolderVideos(Long folderId) {
        List<VideoInfo> allVideos = new ArrayList<>();
        int page = 1;
        int pageSize = 20;
        
        while (true) {
            List<VideoInfo> pageVideos = getFolderVideos(folderId, page, pageSize);
            if (pageVideos.isEmpty()) {
                break;
            }
            
            allVideos.addAll(pageVideos);
            
            // 如果返回的视频数量少于页大小，说明已经是最后一页
            if (pageVideos.size() < pageSize) {
                break;
            }
            
            page++;
            
            // 添加延迟，避免请求过于频繁
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("获取收藏夹所有视频完成: {} - 总计:{}", folderId, allVideos.size());
        return allVideos;
    }
    
    /**
     * 批量获取视频信息
     */
    public List<VideoInfo> getVideosInfo(List<String> aids) {
        if (aids == null || aids.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<VideoInfo> videos = new ArrayList<>();
        
        // 分批处理，每批最多20个
        int batchSize = 20;
        for (int i = 0; i < aids.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, aids.size());
            List<String> batchAids = aids.subList(i, endIndex);
            
            String resources = String.join(",", batchAids.stream()
                    .map(aid -> aid + ":2")
                    .toArray(String[]::new));
            
            try {
                String response = httpUtil.getVideosInfo(resources);
                if (response != null && jsonUtil.isBiliApiSuccess(response)) {
                    JsonNode data = jsonUtil.getBiliApiData(response);
                    if (data != null && data.isArray()) {
                        for (JsonNode videoNode : data) {
                            VideoInfo video = parseVideoInfoFromResource(videoNode);
                            if (video != null) {
                                videos.add(video);
                            }
                        }
                    }
                }
                
                // 添加延迟
                Thread.sleep(1000);
                
            } catch (Exception e) {
                logger.error("批量获取视频信息异常: batch {}-{}", i, endIndex, e);
            }
        }
        
        logger.info("批量获取视频信息完成: {} -> {}", aids.size(), videos.size());
        return videos;
    }
    
    /**
     * 解析视频信息（从收藏夹API）
     */
    private VideoInfo parseVideoInfo(JsonNode mediaNode, Long folderId) {
        try {
            VideoInfo video = new VideoInfo();
            video.setFolderId(folderId);
            video.setBvid(jsonUtil.getString(mediaNode, "bvid"));
            video.setAid(jsonUtil.getString(mediaNode, "id"));
            video.setTitle(jsonUtil.getString(mediaNode, "title"));
            video.setDescription(jsonUtil.getString(mediaNode, "intro"));
            video.setCover(jsonUtil.getString(mediaNode, "cover"));
            video.setDuration(jsonUtil.getInt(mediaNode, "duration"));
            
            // 解析UP主信息
            JsonNode upperNode = mediaNode.get("upper");
            if (upperNode != null) {
                UploaderInfo uploader = new UploaderInfo();
                uploader.setMid(jsonUtil.getLong(upperNode, "mid"));
                uploader.setName(jsonUtil.getString(upperNode, "name"));
                uploader.setFace(jsonUtil.getString(upperNode, "face"));
                video.setUploader(uploader);
            }
            
            // 解析统计信息
            JsonNode cntInfoNode = mediaNode.get("cnt_info");
            if (cntInfoNode != null) {
                VideoStats stats = new VideoStats();
                stats.setPlayCount(jsonUtil.getLong(cntInfoNode, "play"));
                stats.setDanmakuCount(jsonUtil.getLong(cntInfoNode, "danmaku"));
                stats.setReplyCount(jsonUtil.getLong(cntInfoNode, "reply"));
                stats.setFavoriteCount(jsonUtil.getLong(cntInfoNode, "collect"));
                video.setStats(stats);
            }
            
            // 解析时间信息
            Long pubtime = jsonUtil.getLong(mediaNode, "pubtime");
            if (pubtime != null) {
                video.setPublishTime(LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(pubtime), ZoneId.systemDefault()));
            }
            
            Long favTime = jsonUtil.getLong(mediaNode, "fav_time");
            if (favTime != null) {
                video.setFavoriteTime(LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(favTime), ZoneId.systemDefault()));
            }
            
            // 检查视频状态
            Integer attr = jsonUtil.getInt(mediaNode, "attr");
            if (attr != null && attr != 0) {
                video.setValid(false);
                video.setStatus(VideoInfo.VideoStatus.INVALID);
            }
            
            return video;
            
        } catch (Exception e) {
            logger.error("解析视频信息失败", e);
            return null;
        }
    }
    
    /**
     * 解析视频信息（从resource API）
     */
    private VideoInfo parseVideoInfoFromResource(JsonNode videoNode) {
        try {
            VideoInfo video = new VideoInfo();
            video.setBvid(jsonUtil.getString(videoNode, "bvid"));
            video.setAid(jsonUtil.getString(videoNode, "aid"));
            video.setTitle(jsonUtil.getString(videoNode, "title"));
            video.setDescription(jsonUtil.getString(videoNode, "desc"));
            video.setCover(jsonUtil.getString(videoNode, "pic"));
            video.setDuration(jsonUtil.getInt(videoNode, "duration"));
            
            // 解析CID（第一个分P的CID）
            JsonNode pagesNode = videoNode.get("pages");
            if (pagesNode != null && pagesNode.isArray() && pagesNode.size() > 0) {
                JsonNode firstPage = pagesNode.get(0);
                video.setCid(jsonUtil.getLong(firstPage, "cid"));
            }
            
            // 解析UP主信息
            JsonNode ownerNode = videoNode.get("owner");
            if (ownerNode != null) {
                UploaderInfo uploader = new UploaderInfo();
                uploader.setMid(jsonUtil.getLong(ownerNode, "mid"));
                uploader.setName(jsonUtil.getString(ownerNode, "name"));
                uploader.setFace(jsonUtil.getString(ownerNode, "face"));
                video.setUploader(uploader);
            }
            
            // 解析统计信息
            JsonNode statNode = videoNode.get("stat");
            if (statNode != null) {
                VideoStats stats = new VideoStats();
                stats.setPlayCount(jsonUtil.getLong(statNode, "view"));
                stats.setDanmakuCount(jsonUtil.getLong(statNode, "danmaku"));
                stats.setReplyCount(jsonUtil.getLong(statNode, "reply"));
                stats.setFavoriteCount(jsonUtil.getLong(statNode, "favorite"));
                stats.setCoinCount(jsonUtil.getLong(statNode, "coin"));
                stats.setShareCount(jsonUtil.getLong(statNode, "share"));
                stats.setLikeCount(jsonUtil.getLong(statNode, "like"));
                video.setStats(stats);
            }
            
            // 解析发布时间
            Long pubdate = jsonUtil.getLong(videoNode, "pubdate");
            if (pubdate != null) {
                video.setPublishTime(LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(pubdate), ZoneId.systemDefault()));
            }
            
            return video;
            
        } catch (Exception e) {
            logger.error("解析视频信息失败", e);
            return null;
        }
    }
}
