package com.bilibili.monitor.service;

import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件存储服务 - 迁移自原项目的FileStorage
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class StorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(StorageService.class);
    
    @Value("${app.data-path:./data}")
    private String dataPath;
    
    @Autowired
    private JsonUtil jsonUtil;
    
    // 内存缓存
    private final Map<Long, FolderInfo> folderCache = new ConcurrentHashMap<>();
    private final Map<String, VideoInfo> videoCache = new ConcurrentHashMap<>();
    private final Map<String, PreloadInfo> preloadCache = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 创建数据目录
        createDirectories();
        
        // 加载缓存数据
        loadAllData();
        
        logger.info("存储服务初始化完成，数据路径: {}", dataPath);
    }
    
    /**
     * 创建必要的目录结构
     */
    private void createDirectories() {
        try {
            Path dataDir = Paths.get(dataPath);
            Path foldersDir = dataDir.resolve("folders");
            Path videosDir = dataDir.resolve("videos");
            Path preloadDir = dataDir.resolve("preload");
            Path configDir = dataDir.resolve("config");
            
            Files.createDirectories(foldersDir);
            Files.createDirectories(videosDir);
            Files.createDirectories(preloadDir);
            Files.createDirectories(configDir);
            
            logger.info("数据目录创建完成: {}", dataPath);
        } catch (Exception e) {
            logger.error("创建数据目录失败", e);
        }
    }
    
    /**
     * 加载所有数据到缓存
     */
    private void loadAllData() {
        loadFolders();
        loadVideos();
        loadPreloads();
    }
    
    /**
     * 加载收藏夹数据
     */
    private void loadFolders() {
        try {
            Path foldersDir = Paths.get(dataPath, "folders");
            if (!Files.exists(foldersDir)) {
                return;
            }
            
            Files.list(foldersDir)
                    .filter(path -> path.toString().endsWith(".json"))
                    .forEach(path -> {
                        FolderInfo folder = jsonUtil.fromJsonFile(path.toString(), FolderInfo.class);
                        if (folder != null && folder.getFolderId() != null) {
                            folderCache.put(folder.getFolderId(), folder);
                        }
                    });
            
            logger.info("加载收藏夹数据完成: {} 个", folderCache.size());
        } catch (Exception e) {
            logger.error("加载收藏夹数据失败", e);
        }
    }
    
    /**
     * 加载视频数据
     */
    private void loadVideos() {
        try {
            Path videosDir = Paths.get(dataPath, "videos");
            if (!Files.exists(videosDir)) {
                return;
            }
            
            Files.walk(videosDir)
                    .filter(path -> path.toString().endsWith(".json"))
                    .forEach(path -> {
                        VideoInfo video = jsonUtil.fromJsonFile(path.toString(), VideoInfo.class);
                        if (video != null && video.getBvid() != null) {
                            videoCache.put(video.getBvid(), video);
                        }
                    });
            
            logger.info("加载视频数据完成: {} 个", videoCache.size());
        } catch (Exception e) {
            logger.error("加载视频数据失败", e);
        }
    }
    
    /**
     * 加载预下载数据
     */
    private void loadPreloads() {
        try {
            Path preloadDir = Paths.get(dataPath, "preload");
            if (!Files.exists(preloadDir)) {
                return;
            }
            
            Files.walk(preloadDir)
                    .filter(path -> path.toString().endsWith(".json"))
                    .forEach(path -> {
                        PreloadInfo preload = jsonUtil.fromJsonFile(path.toString(), PreloadInfo.class);
                        if (preload != null && preload.getBvid() != null) {
                            preloadCache.put(preload.getBvid(), preload);
                        }
                    });
            
            logger.info("加载预下载数据完成: {} 个", preloadCache.size());
        } catch (Exception e) {
            logger.error("加载预下载数据失败", e);
        }
    }
    
    // ==================== 收藏夹相关操作 ====================
    
    /**
     * 保存收藏夹信息
     */
    public boolean saveFolder(FolderInfo folder) {
        if (folder == null || folder.getFolderId() == null) {
            return false;
        }
        
        try {
            folder.setUpdateTime(LocalDateTime.now());
            String filePath = Paths.get(dataPath, "folders", folder.getFolderId() + ".json").toString();
            boolean success = jsonUtil.toJsonFile(folder, filePath);
            
            if (success) {
                folderCache.put(folder.getFolderId(), folder);
                logger.debug("收藏夹保存成功: {}", folder.getFolderId());
            }
            
            return success;
        } catch (Exception e) {
            logger.error("保存收藏夹失败: {}", folder.getFolderId(), e);
            return false;
        }
    }
    
    /**
     * 获取收藏夹信息
     */
    public FolderInfo getFolder(Long folderId) {
        return folderCache.get(folderId);
    }
    
    /**
     * 获取所有收藏夹
     */
    public List<FolderInfo> getAllFolders() {
        return new ArrayList<>(folderCache.values());
    }
    
    /**
     * 删除收藏夹
     */
    public boolean deleteFolder(Long folderId) {
        try {
            String filePath = Paths.get(dataPath, "folders", folderId + ".json").toString();
            File file = new File(filePath);
            
            boolean deleted = !file.exists() || file.delete();
            if (deleted) {
                folderCache.remove(folderId);
                logger.info("收藏夹删除成功: {}", folderId);
            }
            
            return deleted;
        } catch (Exception e) {
            logger.error("删除收藏夹失败: {}", folderId, e);
            return false;
        }
    }
    
    // ==================== 视频相关操作 ====================
    
    /**
     * 保存视频信息
     */
    public boolean saveVideo(VideoInfo video) {
        if (video == null || video.getBvid() == null || video.getFolderId() == null) {
            return false;
        }
        
        try {
            video.setUpdateTime(LocalDateTime.now());
            String folderPath = Paths.get(dataPath, "videos", video.getFolderId().toString()).toString();
            new File(folderPath).mkdirs();
            
            String filePath = Paths.get(folderPath, video.getBvid() + ".json").toString();
            boolean success = jsonUtil.toJsonFile(video, filePath);
            
            if (success) {
                videoCache.put(video.getBvid(), video);
                logger.debug("视频保存成功: {}", video.getBvid());
            }
            
            return success;
        } catch (Exception e) {
            logger.error("保存视频失败: {}", video.getBvid(), e);
            return false;
        }
    }
    
    /**
     * 获取视频信息
     */
    public VideoInfo getVideo(String bvid) {
        return videoCache.get(bvid);
    }
    
    /**
     * 获取收藏夹的所有视频
     */
    public List<VideoInfo> getFolderVideos(Long folderId) {
        return videoCache.values().stream()
                .filter(video -> folderId.equals(video.getFolderId()))
                .sorted((v1, v2) -> {
                    LocalDateTime t1 = v1.getFavoriteTime();
                    LocalDateTime t2 = v2.getFavoriteTime();
                    if (t1 == null && t2 == null) return 0;
                    if (t1 == null) return 1;
                    if (t2 == null) return -1;
                    return t2.compareTo(t1); // 降序
                })
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 批量保存视频
     */
    public int saveVideos(List<VideoInfo> videos) {
        int successCount = 0;
        for (VideoInfo video : videos) {
            if (saveVideo(video)) {
                successCount++;
            }
        }
        return successCount;
    }
    
    // ==================== 预下载相关操作 ====================
    
    /**
     * 保存预下载信息
     */
    public boolean savePreload(PreloadInfo preload) {
        if (preload == null || preload.getBvid() == null || preload.getFolderId() == null) {
            return false;
        }
        
        try {
            preload.setUpdateTime(LocalDateTime.now());
            String folderPath = Paths.get(dataPath, "preload", preload.getFolderId().toString()).toString();
            new File(folderPath).mkdirs();
            
            String filePath = Paths.get(folderPath, preload.getBvid() + ".json").toString();
            boolean success = jsonUtil.toJsonFile(preload, filePath);
            
            if (success) {
                preloadCache.put(preload.getBvid(), preload);
                logger.debug("预下载信息保存成功: {}", preload.getBvid());
            }
            
            return success;
        } catch (Exception e) {
            logger.error("保存预下载信息失败: {}", preload.getBvid(), e);
            return false;
        }
    }
    
    /**
     * 获取预下载信息
     */
    public PreloadInfo getPreload(String bvid) {
        return preloadCache.get(bvid);
    }
    
    /**
     * 获取收藏夹的所有预下载信息
     */
    public List<PreloadInfo> getFolderPreloads(Long folderId) {
        return preloadCache.values().stream()
                .filter(preload -> folderId.equals(preload.getFolderId()))
                .sorted((p1, p2) -> {
                    LocalDateTime t1 = p1.getCreateTime();
                    LocalDateTime t2 = p2.getCreateTime();
                    if (t1 == null && t2 == null) return 0;
                    if (t1 == null) return 1;
                    if (t2 == null) return -1;
                    return t2.compareTo(t1); // 降序
                })
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 批量保存预下载信息
     */
    public int savePreloads(List<PreloadInfo> preloads) {
        int successCount = 0;
        for (PreloadInfo preload : preloads) {
            if (savePreload(preload)) {
                successCount++;
            }
        }
        return successCount;
    }
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取存储统计信息
     */
    public Map<String, Object> getStorageStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("folderCount", folderCache.size());
        stats.put("videoCount", videoCache.size());
        stats.put("preloadCount", preloadCache.size());
        stats.put("dataPath", dataPath);
        
        // 计算各状态的数量
        long validVideos = videoCache.values().stream()
                .filter(video -> video.getValid() != null && video.getValid())
                .count();
        stats.put("validVideoCount", validVideos);
        stats.put("invalidVideoCount", videoCache.size() - validVideos);
        
        long validPreloads = preloadCache.values().stream()
                .filter(preload -> preload.getCdnValid() != null && preload.getCdnValid())
                .count();
        stats.put("validPreloadCount", validPreloads);
        stats.put("expiredPreloadCount", preloadCache.size() - validPreloads);
        
        return stats;
    }
}
