package com.bilibili.monitor.service;

import com.bilibili.monitor.model.FolderInfo;
import com.bilibili.monitor.model.PreloadInfo;
import com.bilibili.monitor.model.VideoInfo;
import com.bilibili.monitor.strategy.PreloadStrategy;
import com.bilibili.monitor.strategy.SmartPreloadStrategy;
import com.bilibili.monitor.strategy.FullPreloadStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

/**
 * 预下载服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class PreloadService {
    
    private static final Logger logger = LoggerFactory.getLogger(PreloadService.class);
    
    @Value("${app.preload.enabled:true}")
    private boolean preloadEnabled;
    
    @Value("${app.preload.default-strategy:smart}")
    private String defaultStrategy;
    
    @Value("${app.preload.max-concurrent:2}")
    private int maxConcurrent;
    
    @Value("${app.preload.default-interval:60}")
    private int defaultInterval;
    
    @Autowired
    private StorageService storageService;
    
    @Autowired
    private BilibiliApiService bilibiliApiService;
    
    @Autowired
    private CdnService cdnService;
    
    @Autowired
    private WebSocketService webSocketService;
    
    @Autowired
    private SmartPreloadStrategy smartStrategy;
    
    @Autowired
    private FullPreloadStrategy fullStrategy;
    
    // 预下载任务调度器
    private ScheduledExecutorService scheduler;
    
    // 正在预下载的收藏夹
    private final Set<Long> preloadingFolders = ConcurrentHashMap.newKeySet();
    
    // 预下载任务映射
    private final Map<Long, ScheduledFuture<?>> preloadTasks = new ConcurrentHashMap<>();
    
    // 策略映射
    private final Map<String, PreloadStrategy> strategies = new HashMap<>();
    
    @PostConstruct
    public void init() {
        if (!preloadEnabled) {
            logger.info("预下载服务已禁用");
            return;
        }
        
        // 初始化策略映射
        strategies.put("smart", smartStrategy);
        strategies.put("full", fullStrategy);
        
        // 创建调度器
        this.scheduler = Executors.newScheduledThreadPool(maxConcurrent);
        
        // 启动已配置的预下载任务
        startConfiguredPreloads();
        
        logger.info("预下载服务初始化完成，默认策略: {}, 最大并发: {}", defaultStrategy, maxConcurrent);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("预下载服务已关闭");
    }
    
    /**
     * 启动已配置的预下载任务
     */
    private void startConfiguredPreloads() {
        List<FolderInfo> folders = storageService.getAllFolders();
        for (FolderInfo folder : folders) {
            if (folder.getPreloadEnabled() != null && folder.getPreloadEnabled()) {
                startPreload(folder.getFolderId());
            }
        }
    }
    
    /**
     * 启动收藏夹预下载
     */
    public boolean startPreload(Long folderId) {
        if (!preloadEnabled) {
            logger.warn("预下载服务已禁用，无法启动预下载: {}", folderId);
            return false;
        }
        
        if (preloadingFolders.contains(folderId)) {
            logger.info("收藏夹预下载已在运行中: {}", folderId);
            return true;
        }
        
        if (preloadingFolders.size() >= maxConcurrent) {
            logger.warn("预下载任务已达到最大并发数: {}", maxConcurrent);
            return false;
        }
        
        try {
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                logger.error("收藏夹不存在: {}", folderId);
                return false;
            }
            
            // 启用预下载
            folder.setPreloadEnabled(true);
            folder.setPreloadStatus(FolderInfo.PreloadStatus.RUNNING);
            storageService.saveFolder(folder);
            
            // 获取预下载间隔
            int interval = folder.getPreloadInterval() != null ? 
                    folder.getPreloadInterval() : defaultInterval;
            
            // 创建预下载任务
            ScheduledFuture<?> task = scheduler.scheduleWithFixedDelay(
                    () -> executePreloadTask(folderId),
                    0, // 立即开始
                    interval,
                    TimeUnit.MINUTES
            );
            
            preloadTasks.put(folderId, task);
            preloadingFolders.add(folderId);
            
            logger.info("启动收藏夹预下载: {} - 间隔: {}分钟", folderId, interval);
            
            // 发送WebSocket通知
            webSocketService.sendPreloadStatus(folderId, "STARTED");
            
            return true;
            
        } catch (Exception e) {
            logger.error("启动预下载失败: {}", folderId, e);
            return false;
        }
    }
    
    /**
     * 停止收藏夹预下载
     */
    public boolean stopPreload(Long folderId) {
        try {
            ScheduledFuture<?> task = preloadTasks.remove(folderId);
            if (task != null) {
                task.cancel(false);
            }
            
            preloadingFolders.remove(folderId);
            
            // 更新收藏夹状态
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder != null) {
                folder.setPreloadEnabled(false);
                folder.setPreloadStatus(FolderInfo.PreloadStatus.STOPPED);
                storageService.saveFolder(folder);
            }
            
            logger.info("停止收藏夹预下载: {}", folderId);
            
            // 发送WebSocket通知
            webSocketService.sendPreloadStatus(folderId, "STOPPED");
            
            return true;
            
        } catch (Exception e) {
            logger.error("停止预下载失败: {}", folderId, e);
            return false;
        }
    }
    
    /**
     * 执行预下载任务
     */
    @Async
    public void executePreloadTask(Long folderId) {
        try {
            logger.debug("开始执行预下载任务: {}", folderId);
            
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder == null) {
                logger.error("收藏夹不存在: {}", folderId);
                return;
            }
            
            // 获取策略
            PreloadStrategy strategy = getStrategy(folder.getPreloadStrategy());
            
            // 获取视频列表
            List<VideoInfo> allVideos = storageService.getFolderVideos(folderId);
            List<PreloadInfo> existingPreloads = storageService.getFolderPreloads(folderId);
            
            // 使用策略选择需要预下载的视频
            List<VideoInfo> selectedVideos = strategy.selectVideosForPreload(folderId, allVideos, existingPreloads);
            
            if (selectedVideos.isEmpty()) {
                logger.debug("没有视频需要预下载: {}", folderId);
                return;
            }
            
            logger.info("开始预下载视频: {} - {} 个", folderId, selectedVideos.size());
            
            // 执行预下载
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < selectedVideos.size(); i++) {
                VideoInfo video = selectedVideos.get(i);
                
                try {
                    boolean success = preloadVideo(video);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                    
                    // 发送进度更新
                    webSocketService.sendPreloadProgress(folderId, i + 1, selectedVideos.size());
                    
                    // 添加延迟，避免请求过于频繁
                    Thread.sleep(2000);
                    
                } catch (Exception e) {
                    logger.error("预下载视频失败: {} - {}", video.getBvid(), e.getMessage());
                    failCount++;
                }
            }
            
            // 更新收藏夹信息
            folder.setLastPreloadTime(LocalDateTime.now());
            folder.setRecentFailures(failCount);
            storageService.saveFolder(folder);
            
            logger.info("预下载任务完成: {} - 成功:{}, 失败:{}", folderId, successCount, failCount);
            
        } catch (Exception e) {
            logger.error("预下载任务执行失败: {}", folderId, e);
            
            // 更新失败次数
            FolderInfo folder = storageService.getFolder(folderId);
            if (folder != null) {
                folder.setRecentFailures(folder.getRecentFailures() + 1);
                storageService.saveFolder(folder);
            }
        }
    }
    
    /**
     * 预下载单个视频
     */
    private boolean preloadVideo(VideoInfo video) {
        try {
            logger.debug("预下载视频: {} - {}", video.getBvid(), video.getTitle());
            
            // 检查是否已存在预下载信息
            PreloadInfo existingPreload = storageService.getPreload(video.getBvid());
            if (existingPreload != null && existingPreload.getCdnValid() != null && existingPreload.getCdnValid()) {
                logger.debug("视频已有有效预下载: {}", video.getBvid());
                return true;
            }
            
            // 获取CDN链接
            PreloadInfo preloadInfo = cdnService.getCdnLinks(video);
            if (preloadInfo == null) {
                logger.warn("获取CDN链接失败: {}", video.getBvid());
                return false;
            }
            
            // 保存预下载信息
            boolean saved = storageService.savePreload(preloadInfo);
            if (!saved) {
                logger.warn("保存预下载信息失败: {}", video.getBvid());
                return false;
            }
            
            logger.info("预下载视频成功: {} - {}", video.getBvid(), video.getTitle());
            return true;
            
        } catch (Exception e) {
            logger.error("预下载视频异常: {} - {}", video.getBvid(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取预下载策略
     */
    private PreloadStrategy getStrategy(String strategyName) {
        if (strategyName == null || strategyName.trim().isEmpty()) {
            strategyName = defaultStrategy;
        }
        
        PreloadStrategy strategy = strategies.get(strategyName.toLowerCase());
        if (strategy == null) {
            logger.warn("未知的预下载策略: {}，使用默认策略: {}", strategyName, defaultStrategy);
            strategy = strategies.get(defaultStrategy);
        }
        
        return strategy != null ? strategy : smartStrategy; // 最后的保险
    }
    
    /**
     * 获取预下载状态
     */
    public Map<String, Object> getPreloadStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", preloadEnabled);
        status.put("preloadingCount", preloadingFolders.size());
        status.put("maxConcurrent", maxConcurrent);
        status.put("defaultStrategy", defaultStrategy);
        status.put("preloadingFolders", new ArrayList<>(preloadingFolders));
        status.put("availableStrategies", new ArrayList<>(strategies.keySet()));
        return status;
    }
    
    /**
     * 刷新过期的CDN链接
     */
    @Async
    public void refreshExpiredLinks() {
        try {
            logger.debug("开始刷新过期CDN链接");
            
            // 获取所有预下载信息
            List<FolderInfo> folders = storageService.getAllFolders();
            int refreshCount = 0;
            
            for (FolderInfo folder : folders) {
                List<PreloadInfo> preloads = storageService.getFolderPreloads(folder.getFolderId());
                PreloadStrategy strategy = getStrategy(folder.getPreloadStrategy());
                
                for (PreloadInfo preload : preloads) {
                    if (strategy.shouldRefreshLink(preload)) {
                        boolean success = refreshCdnLink(preload);
                        if (success) {
                            refreshCount++;
                        }
                        
                        // 添加延迟
                        Thread.sleep(1000);
                    }
                }
            }
            
            logger.info("CDN链接刷新完成: {} 个", refreshCount);
            
        } catch (Exception e) {
            logger.error("刷新CDN链接失败", e);
        }
    }
    
    /**
     * 刷新单个CDN链接
     */
    private boolean refreshCdnLink(PreloadInfo preloadInfo) {
        try {
            logger.debug("刷新CDN链接: {}", preloadInfo.getBvid());
            
            // 获取视频信息
            VideoInfo video = storageService.getVideo(preloadInfo.getBvid());
            if (video == null) {
                logger.warn("视频信息不存在: {}", preloadInfo.getBvid());
                return false;
            }
            
            // 重新获取CDN链接
            PreloadInfo newPreloadInfo = cdnService.getCdnLinks(video);
            if (newPreloadInfo == null) {
                // 更新失败状态
                preloadInfo.setStatus(PreloadInfo.LinkStatus.FAILED);
                preloadInfo.setRetryCount(preloadInfo.getRetryCount() + 1);
                storageService.savePreload(preloadInfo);
                return false;
            }
            
            // 更新预下载信息
            preloadInfo.setVideoUrl(newPreloadInfo.getVideoUrl());
            preloadInfo.setAudioUrl(newPreloadInfo.getAudioUrl());
            preloadInfo.setCdnObtainTime(newPreloadInfo.getCdnObtainTime());
            preloadInfo.setCdnExpireTime(newPreloadInfo.getCdnExpireTime());
            preloadInfo.setStatus(PreloadInfo.LinkStatus.VALID);
            preloadInfo.setCdnValid(true);
            preloadInfo.setRetryCount(0); // 重置重试次数
            
            boolean saved = storageService.savePreload(preloadInfo);
            if (saved) {
                logger.info("CDN链接刷新成功: {}", preloadInfo.getBvid());
            }
            
            return saved;
            
        } catch (Exception e) {
            logger.error("刷新CDN链接异常: {} - {}", preloadInfo.getBvid(), e.getMessage());
            return false;
        }
    }
}
