package com.bilibili.monitor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 收藏夹信息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FolderInfo {
    
    /** 收藏夹ID */
    private Long folderId;
    
    /** 收藏夹名称 */
    private String title;
    
    /** 收藏夹描述 */
    private String description;
    
    /** 收藏夹封面 */
    private String cover;
    
    /** 收藏夹创建者信息 */
    private UploaderInfo creator;
    
    /** 视频总数 */
    private Integer videoCount = 0;
    
    /** 有效视频数 */
    private Integer validVideoCount = 0;
    
    /** 失效视频数 */
    private Integer invalidVideoCount = 0;
    
    /** 是否公开 */
    private Boolean isPublic = true;
    
    /** 是否启用监控 */
    private Boolean monitorEnabled = false;
    
    /** 监控间隔（分钟） */
    private Integer monitorInterval = 60;
    
    /** 是否启用预下载 */
    private Boolean preloadEnabled = false;
    
    /** 预下载策略 */
    private String preloadStrategy = "smart";
    
    /** 预下载间隔（分钟） */
    private Integer preloadInterval = 60;
    
    /** 最后监控时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastMonitorTime;
    
    /** 最后预下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastPreloadTime;
    
    /** 最近失效视频数 */
    private Integer recentFailures = 0;
    
    /** 监控状态 */
    private MonitorStatus monitorStatus = MonitorStatus.STOPPED;
    
    /** 预下载状态 */
    private PreloadStatus preloadStatus = PreloadStatus.STOPPED;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime = LocalDateTime.now();
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime = LocalDateTime.now();
    
    /** 视频列表 */
    private List<VideoInfo> videos = new ArrayList<>();
    
    // 构造函数
    public FolderInfo() {}
    
    public FolderInfo(Long folderId, String title) {
        this.folderId = folderId;
        this.title = title;
    }
    
    /**
     * 更新视频统计
     */
    public void updateVideoStats() {
        this.videoCount = videos.size();
        this.validVideoCount = (int) videos.stream()
                .filter(video -> video.getValid() != null && video.getValid())
                .count();
        this.invalidVideoCount = this.videoCount - this.validVideoCount;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 获取预下载覆盖率
     */
    public double getPreloadCoverage() {
        if (videoCount == null || videoCount == 0) {
            return 0.0;
        }
        // 这里需要从预下载服务获取实际的预下载数量
        // 暂时返回0，后续实现
        return 0.0;
    }
    
    // Getters and Setters
    public Long getFolderId() { return folderId; }
    public void setFolderId(Long folderId) { this.folderId = folderId; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getCover() { return cover; }
    public void setCover(String cover) { this.cover = cover; }
    
    public UploaderInfo getCreator() { return creator; }
    public void setCreator(UploaderInfo creator) { this.creator = creator; }
    
    public Integer getVideoCount() { return videoCount; }
    public void setVideoCount(Integer videoCount) { this.videoCount = videoCount; }
    
    public Integer getValidVideoCount() { return validVideoCount; }
    public void setValidVideoCount(Integer validVideoCount) { this.validVideoCount = validVideoCount; }
    
    public Integer getInvalidVideoCount() { return invalidVideoCount; }
    public void setInvalidVideoCount(Integer invalidVideoCount) { this.invalidVideoCount = invalidVideoCount; }
    
    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
    
    public Boolean getMonitorEnabled() { return monitorEnabled; }
    public void setMonitorEnabled(Boolean monitorEnabled) { this.monitorEnabled = monitorEnabled; }
    
    public Integer getMonitorInterval() { return monitorInterval; }
    public void setMonitorInterval(Integer monitorInterval) { this.monitorInterval = monitorInterval; }
    
    public Boolean getPreloadEnabled() { return preloadEnabled; }
    public void setPreloadEnabled(Boolean preloadEnabled) { this.preloadEnabled = preloadEnabled; }
    
    public String getPreloadStrategy() { return preloadStrategy; }
    public void setPreloadStrategy(String preloadStrategy) { this.preloadStrategy = preloadStrategy; }
    
    public Integer getPreloadInterval() { return preloadInterval; }
    public void setPreloadInterval(Integer preloadInterval) { this.preloadInterval = preloadInterval; }
    
    public LocalDateTime getLastMonitorTime() { return lastMonitorTime; }
    public void setLastMonitorTime(LocalDateTime lastMonitorTime) { this.lastMonitorTime = lastMonitorTime; }
    
    public LocalDateTime getLastPreloadTime() { return lastPreloadTime; }
    public void setLastPreloadTime(LocalDateTime lastPreloadTime) { this.lastPreloadTime = lastPreloadTime; }
    
    public Integer getRecentFailures() { return recentFailures; }
    public void setRecentFailures(Integer recentFailures) { this.recentFailures = recentFailures; }
    
    public MonitorStatus getMonitorStatus() { return monitorStatus; }
    public void setMonitorStatus(MonitorStatus monitorStatus) { this.monitorStatus = monitorStatus; }
    
    public PreloadStatus getPreloadStatus() { return preloadStatus; }
    public void setPreloadStatus(PreloadStatus preloadStatus) { this.preloadStatus = preloadStatus; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public List<VideoInfo> getVideos() { return videos; }
    public void setVideos(List<VideoInfo> videos) { this.videos = videos; }
    
    /**
     * 监控状态枚举
     */
    public enum MonitorStatus {
        STOPPED("已停止"),
        RUNNING("运行中"),
        PAUSED("已暂停"),
        ERROR("错误");
        
        private final String description;
        
        MonitorStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 预下载状态枚举
     */
    public enum PreloadStatus {
        STOPPED("已停止"),
        RUNNING("运行中"),
        PAUSED("已暂停"),
        ERROR("错误");
        
        private final String description;
        
        PreloadStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    @Override
    public String toString() {
        return "FolderInfo{" +
                "folderId=" + folderId +
                ", title='" + title + '\'' +
                ", videoCount=" + videoCount +
                ", monitorEnabled=" + monitorEnabled +
                ", preloadEnabled=" + preloadEnabled +
                '}';
    }
}
