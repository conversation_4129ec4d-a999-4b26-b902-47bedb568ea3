server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: mebilidown-backend
  
  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  
  # 开发工具配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 应用配置
app:
  # 数据存储路径
  data-path: ./data
  
  # 监控配置
  monitor:
    enabled: true
    default-interval: 60  # 默认监控间隔（分钟）
    max-concurrent: 5     # 最大并发监控数
  
  # 预下载配置
  preload:
    enabled: true
    default-strategy: smart  # 默认策略：smart/full
    max-concurrent: 2        # 最大并发预下载数
    default-interval: 60     # 默认预下载间隔（分钟）
  
  # B站API配置
  bilibili:
    base-url: https://api.bilibili.com
    user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
    timeout: 30000  # 请求超时时间（毫秒）

# 日志配置
logging:
  level:
    com.bilibili.monitor: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ./logs/mebilidown.log
