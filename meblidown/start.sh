#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的文本
print_color() {
    printf "${1}${2}${NC}\n"
}

# 打印标题
print_title() {
    echo
    print_color $BLUE "========================================"
    print_color $BLUE "    MeBiliDown 项目启动脚本"
    print_color $BLUE "========================================"
    echo
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_color $RED "❌ 错误: 未找到 $1，请先安装 $2"
        exit 1
    fi
    print_color $GREEN "✅ $1 环境检查通过"
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_color $YELLOW "⏳ 等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s $url > /dev/null 2>&1; then
            print_color $GREEN "✅ $service_name 启动成功"
            return 0
        fi
        
        printf "."
        sleep 2
        ((attempt++))
    done
    
    echo
    print_color $YELLOW "⚠️  $service_name 启动时间较长，请稍候..."
    return 1
}

# 主函数
main() {
    print_title
    
    # 检查Java环境
    print_color $BLUE "[1/4] 检查Java环境..."
    check_command "java" "Java 17或更高版本"
    
    # 检查Node.js环境
    echo
    print_color $BLUE "[2/4] 检查Node.js环境..."
    check_command "node" "Node.js 16或更高版本"
    check_command "npm" "npm包管理器"
    
    # 获取脚本所在目录
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
    cd "$SCRIPT_DIR"
    
    # 启动后端服务
    echo
    print_color $BLUE "[3/4] 启动后端服务..."
    
    # 检查后端目录
    if [ ! -d "backend" ]; then
        print_color $RED "❌ 错误: 未找到backend目录"
        exit 1
    fi
    
    # 启动后端（后台运行）
    cd backend
    print_color $YELLOW "🚀 正在启动Spring Boot后端服务..."
    
    # 检查是否有Maven
    if command -v mvn &> /dev/null; then
        nohup mvn spring-boot:run > ../backend.log 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > ../backend.pid
    else
        print_color $RED "❌ 错误: 未找到Maven，请先安装Maven"
        exit 1
    fi
    
    cd ..
    
    # 等待后端启动
    wait_for_service "http://localhost:8080/actuator/health" "后端服务"
    
    # 启动前端服务
    echo
    print_color $BLUE "[4/4] 启动前端服务..."
    
    # 检查前端目录
    if [ ! -d "frontend" ]; then
        print_color $RED "❌ 错误: 未找到frontend目录"
        exit 1
    fi
    
    cd frontend
    
    # 检查是否已安装依赖
    if [ ! -d "node_modules" ]; then
        print_color $YELLOW "📦 正在安装前端依赖..."
        npm install
        if [ $? -ne 0 ]; then
            print_color $RED "❌ 错误: 前端依赖安装失败"
            exit 1
        fi
    fi
    
    print_color $YELLOW "🌐 正在启动前端开发服务器..."
    
    # 启动前端（后台运行）
    nohup npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    cd ..
    
    # 等待前端启动
    sleep 5
    wait_for_service "http://localhost:5173" "前端服务"
    
    # 启动完成
    echo
    print_color $BLUE "========================================"
    print_color $BLUE "    启动完成！"
    print_color $BLUE "========================================"
    echo
    print_color $GREEN "🚀 后端服务: http://localhost:8080"
    print_color $GREEN "🌐 前端服务: http://localhost:5173"
    print_color $GREEN "📊 API文档: http://localhost:8080/swagger-ui.html"
    echo
    print_color $YELLOW "💡 提示:"
    print_color $YELLOW "   - 服务已在后台运行"
    print_color $YELLOW "   - 日志文件: backend.log, frontend.log"
    print_color $YELLOW "   - 停止服务: ./stop.sh"
    echo
    
    # 尝试打开浏览器
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:5173
    elif command -v open &> /dev/null; then
        open http://localhost:5173
    else
        print_color $YELLOW "请手动打开浏览器访问: http://localhost:5173"
    fi
    
    print_color $GREEN "🎉 项目启动完成！"
}

# 信号处理
cleanup() {
    echo
    print_color $YELLOW "正在清理进程..."
    
    if [ -f "backend.pid" ]; then
        kill $(cat backend.pid) 2>/dev/null
        rm backend.pid
    fi
    
    if [ -f "frontend.pid" ]; then
        kill $(cat frontend.pid) 2>/dev/null
        rm frontend.pid
    fi
    
    print_color $GREEN "清理完成"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 执行主函数
main

# 保持脚本运行
print_color $YELLOW "按 Ctrl+C 停止所有服务"
while true; do
    sleep 1
done
