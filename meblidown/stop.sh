#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的文本
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "========================================"
print_color $BLUE "    MeBiliDown 停止脚本"
print_color $BLUE "========================================"
echo

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# 停止后端服务
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        print_color $YELLOW "🛑 正在停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $BACKEND_PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            print_color $YELLOW "强制停止后端服务..."
            kill -9 $BACKEND_PID
        fi
        
        print_color $GREEN "✅ 后端服务已停止"
    else
        print_color $YELLOW "⚠️  后端服务进程不存在"
    fi
    rm backend.pid
else
    print_color $YELLOW "⚠️  未找到后端服务PID文件"
fi

# 停止前端服务
if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        print_color $YELLOW "🛑 正在停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $FRONTEND_PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            print_color $YELLOW "强制停止前端服务..."
            kill -9 $FRONTEND_PID
        fi
        
        print_color $GREEN "✅ 前端服务已停止"
    else
        print_color $YELLOW "⚠️  前端服务进程不存在"
    fi
    rm frontend.pid
else
    print_color $YELLOW "⚠️  未找到前端服务PID文件"
fi

# 清理可能残留的进程
print_color $YELLOW "🧹 清理残留进程..."

# 查找并杀死可能的Spring Boot进程
SPRING_PIDS=$(ps aux | grep "spring-boot:run" | grep -v grep | awk '{print $2}')
if [ ! -z "$SPRING_PIDS" ]; then
    echo $SPRING_PIDS | xargs kill -9 2>/dev/null
    print_color $GREEN "✅ 清理了Spring Boot残留进程"
fi

# 查找并杀死可能的Vite进程
VITE_PIDS=$(ps aux | grep "vite" | grep -v grep | awk '{print $2}')
if [ ! -z "$VITE_PIDS" ]; then
    echo $VITE_PIDS | xargs kill -9 2>/dev/null
    print_color $GREEN "✅ 清理了Vite残留进程"
fi

# 查找并杀死占用端口的进程
check_and_kill_port() {
    local port=$1
    local service_name=$2
    
    local pid=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pid" ]; then
        print_color $YELLOW "发现端口 $port 被进程 $pid 占用，正在清理..."
        kill -9 $pid 2>/dev/null
        print_color $GREEN "✅ 清理了 $service_name 端口占用"
    fi
}

check_and_kill_port 8080 "后端服务"
check_and_kill_port 5173 "前端服务"

echo
print_color $BLUE "========================================"
print_color $GREEN "    所有服务已停止"
print_color $BLUE "========================================"
echo

# 显示日志文件信息
if [ -f "backend.log" ]; then
    print_color $YELLOW "📄 后端日志: backend.log"
fi

if [ -f "frontend.log" ]; then
    print_color $YELLOW "📄 前端日志: frontend.log"
fi

echo
print_color $GREEN "🎉 停止完成！"
