{"name": "mebilidown-frontend", "version": "1.0.0", "description": "MeBiliDown 前端 - 现代化B站收藏夹监控与预下载系统", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "element-plus": "^2.3.9", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.9", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "typescript": "^5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}, "keywords": ["bilibili", "monitor", "preload", "vue3", "typescript", "element-plus"], "author": "MeBiliDown", "license": "MIT"}