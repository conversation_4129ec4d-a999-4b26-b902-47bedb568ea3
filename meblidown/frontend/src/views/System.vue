<template>
  <div class="system-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          系统状态
        </h1>
        <p class="page-subtitle">监控系统运行状态和性能指标</p>
      </div>
      <div class="header-actions">
        <el-button
          type="warning"
          :icon="Delete"
          @click="triggerGC"
          :loading="gcLoading"
        >
          垃圾回收
        </el-button>
        <el-button :icon="Refresh" @click="refreshData" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 系统健康状态 -->
    <el-card class="health-card" :class="{ 'health-error': !systemStore.isHealthy }">
      <div class="health-indicator">
        <div class="health-icon" :class="{ 'healthy': systemStore.isHealthy }">
          <el-icon :size="32">
            <component :is="healthIcon" />
          </el-icon>
        </div>
        <div class="health-content">
          <h2 class="health-title">{{ healthTitle }}</h2>
          <p class="health-description">{{ healthDescription }}</p>
          <div class="health-meta">
            最后检查: {{ lastUpdateTime }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 系统指标 -->
    <el-row :gutter="20" class="metrics-row">
      <!-- 内存使用 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="metric-card">
          <div class="metric-header">
            <el-icon><Cpu /></el-icon>
            <span>内存使用</span>
          </div>
          <div class="metric-content">
            <div class="metric-chart">
              <el-progress
                type="circle"
                :percentage="memoryUsage"
                :color="memoryColor"
                :width="80"
              >
                <span class="progress-text">{{ memoryUsage.toFixed(1) }}%</span>
              </el-progress>
            </div>
            <div class="metric-details" v-if="systemStatus?.memory">
              <div class="detail-item">
                <span>已用:</span>
                <span>{{ formatMemory(systemStatus.memory.used) }}</span>
              </div>
              <div class="detail-item">
                <span>可用:</span>
                <span>{{ formatMemory(systemStatus.memory.free) }}</span>
              </div>
              <div class="detail-item">
                <span>最大:</span>
                <span>{{ formatMemory(systemStatus.memory.max) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 存储统计 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="metric-card">
          <div class="metric-header">
            <el-icon><FolderOpened /></el-icon>
            <span>存储统计</span>
          </div>
          <div class="metric-content">
            <div class="storage-stats" v-if="storageStats">
              <div class="stat-item">
                <div class="stat-value">{{ storageStats.folderCount || 0 }}</div>
                <div class="stat-label">收藏夹</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ storageStats.videoCount || 0 }}</div>
                <div class="stat-label">视频</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ storageStats.preloadCount || 0 }}</div>
                <div class="stat-label">预下载</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 监控状态 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="metric-card">
          <div class="metric-header">
            <el-icon><View /></el-icon>
            <span>监控状态</span>
          </div>
          <div class="metric-content">
            <div class="monitor-stats" v-if="monitorStats">
              <div class="stat-item">
                <div class="stat-value" :class="{ 'active': monitorStats.enabled }">
                  {{ monitorStats.enabled ? '启用' : '禁用' }}
                </div>
                <div class="stat-label">监控服务</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ monitorStats.monitoringCount || 0 }}</div>
                <div class="stat-label">运行中</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ monitorStats.maxConcurrent || 0 }}</div>
                <div class="stat-label">最大并发</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- WebSocket连接 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="metric-card">
          <div class="metric-header">
            <el-icon><Connection /></el-icon>
            <span>WebSocket</span>
          </div>
          <div class="metric-content">
            <div class="websocket-stats" v-if="websocketStats">
              <div class="stat-item">
                <div class="stat-value">{{ websocketStats.totalConnections || 0 }}</div>
                <div class="stat-label">总连接数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value active">{{ websocketStats.activeConnections || 0 }}</div>
                <div class="stat-label">活跃连接</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="20" class="info-row">
      <!-- 系统配置 -->
      <el-col :xs="24" :lg="12">
        <el-card class="info-card">
          <template #header>
            <span>系统配置</span>
          </template>
          <div class="config-list" v-if="systemConfig">
            <div class="config-item" v-for="(value, key) in displayConfig" :key="key">
              <span class="config-key">{{ key }}:</span>
              <span class="config-value">{{ value }}</span>
            </div>
          </div>
          <el-empty v-else description="配置信息加载中..." />
        </el-card>
      </el-col>

      <!-- 系统信息 -->
      <el-col :xs="24" :lg="12">
        <el-card class="info-card">
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="info-list" v-if="systemInfo">
            <div class="info-item" v-for="(value, key) in displayInfo" :key="key">
              <span class="info-key">{{ key }}:</span>
              <span class="info-value">{{ value }}</span>
            </div>
          </div>
          <el-empty v-else description="系统信息加载中..." />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Setting,
  Delete,
  Refresh,
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  Cpu,
  FolderOpened,
  View,
  Connection
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '@/stores/system'
import { formatTime, formatFileSize } from '@/utils'

// 状态管理
const systemStore = useSystemStore()

// 响应式状态
const loading = ref(false)
const gcLoading = ref(false)

// 计算属性
const systemStatus = computed(() => systemStore.systemStatus)
const systemInfo = computed(() => systemStore.systemInfo)
const systemConfig = computed(() => systemStore.systemConfig)
const storageStats = computed(() => systemStore.storageStats)
const monitorStats = computed(() => systemStore.monitorStats)
const websocketStats = computed(() => systemStore.websocketStats)

const memoryUsage = computed(() => systemStore.memoryUsage)
const memoryColor = computed(() => systemStore.memoryUsageColor)

const healthIcon = computed(() => {
  if (systemStore.isHealthy) return CircleCheckFilled
  if (systemStatus.value) return WarningFilled
  return CircleCloseFilled
})

const healthTitle = computed(() => {
  if (systemStore.isHealthy) return '系统运行正常'
  if (systemStatus.value) return '系统存在警告'
  return '系统运行异常'
})

const healthDescription = computed(() => {
  if (systemStore.isHealthy) {
    return '所有系统组件运行正常，性能指标在正常范围内'
  } else {
    return '系统存在问题，请检查相关组件状态'
  }
})

const lastUpdateTime = computed(() => {
  const time = systemStore.lastUpdateTime
  return time ? formatTime.relative(time) : '未知'
})

const displayConfig = computed(() => {
  if (!systemConfig.value) return {}
  
  const config: Record<string, any> = {}
  Object.entries(systemConfig.value).forEach(([key, value]) => {
    // 格式化配置项显示
    const displayKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    config[displayKey] = typeof value === 'boolean' ? (value ? '启用' : '禁用') : value
  })
  
  return config
})

const displayInfo = computed(() => {
  if (!systemInfo.value) return {}
  
  const info: Record<string, any> = {}
  Object.entries(systemInfo.value).forEach(([key, value]) => {
    // 格式化信息项显示
    const displayKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    info[displayKey] = value
  })
  
  return info
})

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    await systemStore.fetchAllData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const triggerGC = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要执行垃圾回收吗？这可能会暂时影响系统性能。',
      '垃圾回收确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    gcLoading.value = true
    await systemStore.triggerGC()
    ElMessage.success('垃圾回收执行成功')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '垃圾回收执行失败')
    }
  } finally {
    gcLoading.value = false
  }
}

const formatMemory = (bytes: number) => {
  return formatFileSize(bytes)
}

// 自动刷新
let refreshTimer: number | null = null

onMounted(async () => {
  await refreshData()
  
  // 启动自动刷新
  refreshTimer = window.setInterval(() => {
    systemStore.fetchSystemStatus()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.system-page {
  padding: 0;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.header-left .page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 健康状态卡片 */
.health-card {
  margin-bottom: 24px;
  border-left: 4px solid var(--el-color-success);
  transition: all 0.3s ease;
}

.health-card.health-error {
  border-left-color: var(--el-color-danger);
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 20px;
}

.health-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  transition: all 0.3s ease;
}

.health-icon.healthy {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.health-content {
  flex: 1;
}

.health-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.health-description {
  color: var(--el-text-color-secondary);
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.health-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 指标行 */
.metrics-row {
  margin-bottom: 24px;
}

.metric-card {
  height: 200px;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.metric-content {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 内存使用图表 */
.metric-chart {
  text-align: center;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 统计项 */
.storage-stats,
.monitor-stats,
.websocket-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-value.active {
  color: var(--el-color-success);
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 信息行 */
.info-row {
  margin-bottom: 24px;
}

.info-card {
  height: 300px;
}

.info-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  overflow-y: auto;
}

/* 配置列表 */
.config-list,
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.config-item:last-child,
.info-item:last-child {
  border-bottom: none;
}

.config-key,
.info-key {
  font-weight: 500;
  color: var(--el-text-color-regular);
  flex: 1;
}

.config-value,
.info-value {
  color: var(--el-text-color-primary);
  text-align: right;
  word-break: break-all;
}

/* 响应式 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .health-indicator {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .metric-card {
    height: auto;
    min-height: 150px;
  }

  .storage-stats,
  .monitor-stats,
  .websocket-stats {
    flex-direction: column;
    gap: 12px;
  }

  .config-item,
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .config-value,
  .info-value {
    text-align: left;
  }
}
</style>
