<template>
  <div class="preload-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Download /></el-icon>
          预下载管理
        </h1>
        <p class="page-subtitle">管理视频预下载任务，获取CDN直链</p>
      </div>
      <div class="header-actions">
        <el-button
          type="success"
          :icon="VideoPlay"
          @click="startAllPreloads"
          :loading="preloadStore.loading"
          :disabled="!preloadStore.canStartMore"
        >
          批量启动
        </el-button>
        <el-button
          type="danger"
          :icon="VideoPause"
          @click="stopAllPreloads"
          :loading="preloadStore.loading"
          :disabled="preloadStore.preloadingCount === 0"
        >
          批量停止
        </el-button>
        <el-button
          type="warning"
          :icon="RefreshRight"
          @click="refreshExpiredLinks"
        >
          刷新过期链接
        </el-button>
        <el-button :icon="Refresh" @click="refreshData" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 预下载状态概览 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon running">
            <el-icon><Download /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ preloadStore.preloadingCount }}</div>
            <div class="status-label">预下载中</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon strategy">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ preloadStore.defaultStrategy }}</div>
            <div class="status-label">默认策略</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon capacity">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ preloadStore.maxConcurrent }}</div>
            <div class="status-label">最大并发</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon rate">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ preloadingRate }}%</div>
            <div class="status-label">预下载覆盖率</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 预下载任务列表 -->
    <el-card class="preload-card">
      <template #header>
        <div class="card-header">
          <span>预下载任务</span>
          <div class="header-tools">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="已停止" value="stopped" />
              <el-option label="错误" value="error" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="filteredFolders"
        stripe
        class="preload-table"
      >
        <el-table-column label="收藏夹信息" min-width="300">
          <template #default="{ row }">
            <div class="folder-info">
              <el-avatar :src="row.cover" :size="50" shape="square">
                <el-icon><Folder /></el-icon>
              </el-avatar>
              <div class="folder-details">
                <div class="folder-title">{{ row.title }}</div>
                <div class="folder-meta">
                  ID: {{ row.folderId }} | {{ row.videoCount }} 个视频
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="预下载状态" width="120" align="center">
          <template #default="{ row }">
            <div class="status-indicator">
              <div class="status-dot" :class="getStatusClass(row.preloadStatus)"></div>
              <span>{{ getStatusText(row.preloadStatus) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="策略配置" width="150" align="center">
          <template #default="{ row }">
            <div class="preload-config">
              <div>策略: {{ getStrategyDisplayName(row.preloadStrategy) }}</div>
              <div>间隔: {{ row.preloadInterval || 60 }}分钟</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="进度" width="150" align="center">
          <template #default="{ row }">
            <div class="progress-info" v-if="preloadStore.isPreloading(row.folderId)">
              <el-progress
                :percentage="getProgressPercentage(row.folderId)"
                :stroke-width="6"
                :show-text="false"
              />
              <div class="progress-text">
                {{ getProgressText(row.folderId) }}
              </div>
            </div>
            <div v-else class="no-progress">
              {{ row.lastPreloadTime ? formatTime.relative(row.lastPreloadTime) : '从未' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                :type="preloadStore.isPreloading(row.folderId) ? 'danger' : 'success'"
                size="small"
                @click="togglePreload(row)"
                :loading="preloadStore.loading"
              >
                {{ preloadStore.isPreloading(row.folderId) ? '停止' : '启动' }}
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="executePreload(row)"
                :disabled="!row.preloadEnabled"
              >
                立即执行
              </el-button>
              <el-dropdown @command="(cmd) => handleCommand(cmd, row)" trigger="click">
                <el-button size="small" :icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="config">配置</el-dropdown-item>
                    <el-dropdown-item command="preloads">预下载列表</el-dropdown-item>
                    <el-dropdown-item command="stats">统计信息</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="预下载配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="currentConfigFolder"
        :model="configForm"
        :rules="configRules"
        ref="configFormRef"
        label-width="120px"
      >
        <el-form-item label="启用预下载" prop="preloadEnabled">
          <el-switch v-model="configForm.preloadEnabled" />
        </el-form-item>
        <el-form-item label="预下载策略" prop="preloadStrategy">
          <el-select v-model="configForm.preloadStrategy" style="width: 100%;">
            <el-option
              v-for="strategy in preloadStore.availableStrategies"
              :key="strategy"
              :label="getStrategyDisplayName(strategy)"
              :value="strategy"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预下载间隔" prop="preloadInterval">
          <el-input-number
            v-model="configForm.preloadInterval"
            :min="15"
            :max="1440"
            :step="15"
            controls-position="right"
          />
          <span class="form-unit">分钟</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="configSaving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Download,
  VideoPlay,
  VideoPause,
  RefreshRight,
  Refresh,
  Setting,
  TrendCharts,
  CircleCheck,
  Folder,
  More
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useFoldersStore } from '@/stores/folders'
import { usePreloadStore } from '@/stores/preload'
import { formatTime, getStatusText } from '@/utils'
import type { FolderInfo } from '@/types'

// 路由
const router = useRouter()

// 状态管理
const foldersStore = useFoldersStore()
const preloadStore = usePreloadStore()

// 响应式状态
const loading = ref(false)
const statusFilter = ref('')
const configDialogVisible = ref(false)
const configSaving = ref(false)
const currentConfigFolder = ref<FolderInfo | null>(null)
const configFormRef = ref<FormInstance>()

// 配置表单
const configForm = ref({
  preloadEnabled: false,
  preloadStrategy: 'smart',
  preloadInterval: 60
})

// 表单验证规则
const configRules: FormRules = {
  preloadStrategy: [
    { required: true, message: '请选择预下载策略', trigger: 'change' }
  ],
  preloadInterval: [
    { required: true, message: '请输入预下载间隔', trigger: 'blur' },
    { type: 'number', min: 15, max: 1440, message: '预下载间隔必须在15-1440分钟之间', trigger: 'blur' }
  ]
}

// 计算属性
const filteredFolders = computed(() => {
  let folders = foldersStore.folders

  if (statusFilter.value) {
    folders = folders.filter(folder => {
      switch (statusFilter.value) {
        case 'running':
          return folder.preloadStatus === 'RUNNING'
        case 'stopped':
          return folder.preloadStatus === 'STOPPED'
        case 'error':
          return folder.preloadStatus === 'ERROR'
        default:
          return true
      }
    })
  }

  return folders
})

const preloadingRate = computed(() => {
  const total = foldersStore.totalFolders
  return total > 0 ? ((preloadStore.preloadingCount / total) * 100).toFixed(1) : '0'
})

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    await Promise.all([
      foldersStore.fetchFolders(),
      preloadStore.fetchPreloadStatus()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const togglePreload = async (folder: FolderInfo) => {
  try {
    await preloadStore.togglePreload(folder.folderId)
    ElMessage.success('操作成功')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  }
}

const executePreload = async (folder: FolderInfo) => {
  try {
    await preloadStore.executePreload(folder.folderId)
    ElMessage.success('预下载任务已开始执行')
  } catch (error: any) {
    ElMessage.error(error.message || '执行失败')
  }
}

const startAllPreloads = async () => {
  try {
    const result = await preloadStore.startAllPreloads()
    ElMessage.success(`批量启动完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)
  } catch (error: any) {
    ElMessage.error(error.message || '批量启动失败')
  }
}

const stopAllPreloads = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有预下载任务吗？',
      '批量停止确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await preloadStore.stopAllPreloads()
    ElMessage.success(`批量停止完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)

  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量停止失败')
    }
  }
}

const refreshExpiredLinks = async () => {
  try {
    await preloadStore.refreshExpiredLinks()
    ElMessage.success('过期链接刷新任务已开始执行')
  } catch (error: any) {
    ElMessage.error(error.message || '刷新失败')
  }
}

const handleCommand = (command: string, folder: FolderInfo) => {
  switch (command) {
    case 'config':
      showConfigDialog(folder)
      break
    case 'preloads':
      router.push(`/folders/${folder.folderId}`)
      break
    case 'stats':
      ElMessage.info('统计功能开发中')
      break
  }
}

const showConfigDialog = (folder: FolderInfo) => {
  currentConfigFolder.value = folder
  configForm.value = {
    preloadEnabled: folder.preloadEnabled || false,
    preloadStrategy: folder.preloadStrategy || 'smart',
    preloadInterval: folder.preloadInterval || 60
  }
  configDialogVisible.value = true
}

const saveConfig = async () => {
  if (!configFormRef.value || !currentConfigFolder.value) return

  try {
    await configFormRef.value.validate()
    configSaving.value = true

    await preloadStore.updatePreloadConfig(currentConfigFolder.value.folderId, configForm.value)

    ElMessage.success('配置保存成功')
    configDialogVisible.value = false

  } catch (error: any) {
    if (error.fields) {
      return
    }
    ElMessage.error(error.message || '配置保存失败')
  } finally {
    configSaving.value = false
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'RUNNING': return 'running'
    case 'STOPPED': return 'stopped'
    case 'ERROR': return 'error'
    default: return 'stopped'
  }
}

const getStrategyDisplayName = (strategy: string) => {
  return preloadStore.getStrategyDisplayName(strategy)
}

const getProgressPercentage = (folderId: number) => {
  const progress = preloadStore.getPreloadProgress(folderId)
  return progress.total > 0 ? Math.round((progress.completed / progress.total) * 100) : 0
}

const getProgressText = (folderId: number) => {
  const progress = preloadStore.getPreloadProgress(folderId)
  return `${progress.completed}/${progress.total}`
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.preload-page {
  padding: 0;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.header-left .page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 状态概览 */
.status-overview {
  margin-bottom: 24px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.status-icon.running {
  background: #67c23a20;
  color: #67c23a;
}

.status-icon.strategy {
  background: #409eff20;
  color: #409eff;
}

.status-icon.capacity {
  background: #e6a23c20;
  color: #e6a23c;
}

.status-icon.rate {
  background: #f56c6c20;
  color: #f56c6c;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* 收藏夹信息 */
.folder-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.folder-details {
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.running {
  background: #67c23a;
  animation: pulse 2s infinite;
}

.status-dot.stopped {
  background: #909399;
}

.status-dot.error {
  background: #f56c6c;
}

/* 预下载配置 */
.preload-config {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* 进度信息 */
.progress-info {
  text-align: center;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.no-progress {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 表单单位 */
.form-unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
