<template>
  <div class="folders-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Folder /></el-icon>
          收藏夹管理
        </h1>
        <p class="page-subtitle">管理B站收藏夹，配置监控和预下载</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showAddDialog">
          添加收藏夹
        </el-button>
        <el-button :icon="Refresh" @click="refreshData" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-row">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ foldersStore.totalFolders }}</div>
            <div class="stat-label">收藏夹总数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ foldersStore.totalVideos }}</div>
            <div class="stat-label">视频总数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ monitorStore.monitoringCount }}</div>
            <div class="stat-label">监控中</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ preloadStore.preloadingCount }}</div>
            <div class="stat-label">预下载中</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 收藏夹列表 -->
    <el-card class="folders-card">
      <template #header>
        <div class="card-header">
          <span>收藏夹列表</span>
          <div class="header-tools">
            <el-input
              v-model="searchText"
              placeholder="搜索收藏夹..."
              :prefix-icon="Search"
              clearable
              style="width: 200px;"
            />
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="filteredFolders"
        stripe
        @row-click="handleRowClick"
        class="folders-table"
      >
        <el-table-column label="收藏夹信息" min-width="300">
          <template #default="{ row }">
            <div class="folder-info">
              <el-avatar :src="row.cover" :size="50" shape="square">
                <el-icon><Folder /></el-icon>
              </el-avatar>
              <div class="folder-details">
                <div class="folder-title">{{ row.title }}</div>
                <div class="folder-meta">
                  ID: {{ row.folderId }} | 
                  {{ row.videoCount }} 个视频 |
                  {{ row.creator?.name || '未知' }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="监控状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getMonitorTagType(row.monitorStatus)"
              size="small"
            >
              {{ getStatusText(row.monitorStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="预下载状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getPreloadTagType(row.preloadStatus)"
              size="small"
            >
              {{ getStatusText(row.preloadStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="最后更新" width="150" align="center">
          <template #default="{ row }">
            <div class="update-time">
              {{ formatTime.relative(row.updateTime) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                :type="monitorStore.isMonitoring(row.folderId) ? 'danger' : 'success'"
                size="small"
                @click.stop="toggleMonitor(row)"
                :loading="monitorStore.loading"
              >
                {{ monitorStore.isMonitoring(row.folderId) ? '停止监控' : '开始监控' }}
              </el-button>
              <el-dropdown @command="(cmd) => handleCommand(cmd, row)" trigger="click">
                <el-button size="small" :icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                    <el-dropdown-item command="config">配置</el-dropdown-item>
                    <el-dropdown-item command="refresh">刷新</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加收藏夹对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加收藏夹"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="收藏夹ID" prop="folderId">
          <el-input
            v-model="addForm.folderId"
            placeholder="请输入B站收藏夹ID"
            clearable
          />
          <div class="form-tip">
            可以从收藏夹URL中获取ID，例如：https://space.bilibili.com/xxx/favlist?fid=123456789
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAdd" :loading="adding">
          添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Folder,
  Plus,
  Refresh,
  Search,
  More
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { formatTime, getStatusText } from '@/utils'
import type { FolderInfo } from '@/types'

// 路由
const router = useRouter()

// 状态管理
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 响应式状态
const loading = ref(false)
const adding = ref(false)
const searchText = ref('')
const addDialogVisible = ref(false)
const addFormRef = ref<FormInstance>()

// 表单数据
const addForm = ref({
  folderId: ''
})

// 表单验证规则
const addRules: FormRules = {
  folderId: [
    { required: true, message: '请输入收藏夹ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '收藏夹ID必须是数字', trigger: 'blur' }
  ]
}

// 计算属性
const filteredFolders = computed(() => {
  if (!searchText.value) {
    return foldersStore.folders
  }
  
  const keyword = searchText.value.toLowerCase()
  return foldersStore.folders.filter(folder =>
    folder.title.toLowerCase().includes(keyword) ||
    folder.folderId.toString().includes(keyword) ||
    folder.creator?.name?.toLowerCase().includes(keyword)
  )
})

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    await Promise.all([
      foldersStore.fetchFolders(),
      monitorStore.fetchMonitorStatus(),
      preloadStore.fetchPreloadStatus()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const showAddDialog = () => {
  addForm.value.folderId = ''
  addDialogVisible.value = true
}

const handleAdd = async () => {
  if (!addFormRef.value) return
  
  try {
    await addFormRef.value.validate()
    adding.value = true
    
    const folderId = parseInt(addForm.value.folderId)
    await foldersStore.addFolder(folderId)
    
    ElMessage.success('收藏夹添加成功')
    addDialogVisible.value = false
    
  } catch (error: any) {
    if (error.fields) {
      // 表单验证错误
      return
    }
    ElMessage.error(error.message || '添加收藏夹失败')
  } finally {
    adding.value = false
  }
}

const handleRowClick = (row: FolderInfo) => {
  router.push(`/folders/${row.folderId}`)
}

const toggleMonitor = async (folder: FolderInfo) => {
  try {
    if (monitorStore.isMonitoring(folder.folderId)) {
      await monitorStore.stopMonitor(folder.folderId)
      ElMessage.success('监控已停止')
    } else {
      await monitorStore.startMonitor(folder.folderId)
      ElMessage.success('监控已启动')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  }
}

const handleCommand = async (command: string, folder: FolderInfo) => {
  switch (command) {
    case 'detail':
      router.push(`/folders/${folder.folderId}`)
      break
      
    case 'config':
      // TODO: 打开配置对话框
      ElMessage.info('配置功能开发中')
      break
      
    case 'refresh':
      try {
        await foldersStore.refreshFolder(folder.folderId)
        ElMessage.success('收藏夹刷新成功')
      } catch (error: any) {
        ElMessage.error(error.message || '刷新失败')
      }
      break
      
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除收藏夹"${folder.title}"吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await foldersStore.deleteFolder(folder.folderId)
        ElMessage.success('收藏夹删除成功')
        
      } catch (error: any) {
        if (error !== 'cancel') {
          ElMessage.error(error.message || '删除失败')
        }
      }
      break
  }
}

const getMonitorTagType = (status: string) => {
  switch (status) {
    case 'RUNNING': return 'success'
    case 'STOPPED': return 'info'
    case 'ERROR': return 'danger'
    default: return 'info'
  }
}

const getPreloadTagType = (status: string) => {
  switch (status) {
    case 'RUNNING': return 'success'
    case 'STOPPED': return 'info'
    case 'ERROR': return 'danger'
    default: return 'info'
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.folders-page {
  padding: 0;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.header-left .page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计信息 */
.stats-row {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* 收藏夹信息 */
.folder-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.folder-details {
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 更新时间 */
.update-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 表单提示 */
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

/* 响应式 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .folders-table {
    font-size: 14px;
  }
  
  .folder-info {
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
