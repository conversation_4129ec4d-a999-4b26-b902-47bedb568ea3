<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Monitor /></el-icon>
        仪表板
      </h1>
      <p class="page-subtitle">系统概览与实时监控</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <StatCard
        v-for="card in statCards"
        :key="card.title"
        :title="card.title"
        :value="card.value"
        :icon="card.icon"
        :color="card.color"
        :trend="card.trend"
        :loading="loading"
      />
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 系统状态图表 -->
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button :icon="Refresh" size="small" @click="refreshSystemData" />
            </div>
          </template>
          <SystemChart :loading="loading" />
        </el-card>
      </el-col>

      <!-- 收藏夹分布图表 -->
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>收藏夹分布</span>
              <el-button :icon="Refresh" size="small" @click="refreshFolderData" />
            </div>
          </template>
          <FolderChart :loading="foldersStore.loading" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 活动列表 -->
    <el-row :gutter="20" class="activity-row">
      <!-- 最近活动 -->
      <el-col :xs="24" :lg="16">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button :icon="Refresh" size="small" @click="refreshActivity" />
            </div>
          </template>
          <ActivityList :loading="loading" />
        </el-card>
      </el-col>

      <!-- 快速操作 -->
      <el-col :xs="24" :lg="8">
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>
          <QuickActions />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { Monitor, Refresh } from '@element-plus/icons-vue'
import StatCard from '@/components/common/StatCard.vue'
import SystemChart from '@/components/charts/SystemChart.vue'
import FolderChart from '@/components/charts/FolderChart.vue'
import ActivityList from '@/components/dashboard/ActivityList.vue'
import QuickActions from '@/components/dashboard/QuickActions.vue'
import { useSystemStore } from '@/stores/system'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { formatNumber } from '@/utils'

// 状态管理
const systemStore = useSystemStore()
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 计算属性
const loading = computed(() => 
  systemStore.loading || foldersStore.loading
)

const statCards = computed(() => [
  {
    title: '收藏夹总数',
    value: formatNumber(foldersStore.totalFolders),
    icon: 'Folder',
    color: '#409eff',
    trend: {
      value: 0,
      isUp: true
    }
  },
  {
    title: '视频总数',
    value: formatNumber(foldersStore.totalVideos),
    icon: 'VideoPlay',
    color: '#67c23a',
    trend: {
      value: 0,
      isUp: true
    }
  },
  {
    title: '监控任务',
    value: monitorStore.monitoringCount,
    icon: 'View',
    color: '#e6a23c',
    trend: {
      value: 0,
      isUp: monitorStore.monitoringCount > 0
    }
  },
  {
    title: '预下载任务',
    value: preloadStore.preloadingCount,
    icon: 'Download',
    color: '#f56c6c',
    trend: {
      value: 0,
      isUp: preloadStore.preloadingCount > 0
    }
  },
  {
    title: '内存使用率',
    value: `${systemStore.memoryUsage.toFixed(1)}%`,
    icon: 'Cpu',
    color: systemStore.memoryUsageColor,
    trend: {
      value: 0,
      isUp: false
    }
  },
  {
    title: '有效视频率',
    value: `${foldersStore.validRate.toFixed(1)}%`,
    icon: 'SuccessFilled',
    color: '#67c23a',
    trend: {
      value: 0,
      isUp: true
    }
  }
])

// 方法
const refreshSystemData = async () => {
  await systemStore.fetchSystemStatus()
}

const refreshFolderData = async () => {
  await foldersStore.fetchFolders()
}

const refreshActivity = async () => {
  // 刷新活动数据的逻辑
  console.log('刷新活动数据')
}

// 自动刷新
let refreshTimer: number | null = null

onMounted(async () => {
  // 初始化数据
  await Promise.all([
    systemStore.fetchAllData(),
    foldersStore.fetchFolders(),
    monitorStore.fetchMonitorStatus(),
    preloadStore.fetchPreloadStatus()
  ])

  // 启动自动刷新
  refreshTimer = window.setInterval(() => {
    systemStore.fetchSystemStatus()
    monitorStore.fetchMonitorStatus()
    preloadStore.fetchPreloadStatus()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

/* 图表行 */
.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 20px;
}

/* 活动行 */
.activity-row {
  margin-bottom: 24px;
}

.activity-card {
  height: 500px;
}

.activity-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 20px;
  overflow-y: auto;
}

.quick-actions-card {
  height: 500px;
}

.quick-actions-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

/* 响应式 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }
  
  .charts-row,
  .activity-row {
    margin-bottom: 16px;
  }
  
  .chart-card,
  .activity-card,
  .quick-actions-card {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}
</style>
