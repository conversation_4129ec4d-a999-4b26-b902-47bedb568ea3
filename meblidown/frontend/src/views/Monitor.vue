<template>
  <div class="monitor-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><View /></el-icon>
          监控管理
        </h1>
        <p class="page-subtitle">管理收藏夹监控任务，实时检测视频变化</p>
      </div>
      <div class="header-actions">
        <el-button
          type="success"
          :icon="VideoPlay"
          @click="startAllMonitors"
          :loading="monitorStore.loading"
          :disabled="!monitorStore.canStartMore"
        >
          批量启动
        </el-button>
        <el-button
          type="danger"
          :icon="VideoPause"
          @click="stopAllMonitors"
          :loading="monitorStore.loading"
          :disabled="monitorStore.monitoringCount === 0"
        >
          批量停止
        </el-button>
        <el-button :icon="Refresh" @click="refreshData" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 监控状态概览 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon running">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ monitorStore.monitoringCount }}</div>
            <div class="status-label">监控中</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon total">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ foldersStore.totalFolders }}</div>
            <div class="status-label">总收藏夹</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon capacity">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ monitorStore.maxConcurrent }}</div>
            <div class="status-label">最大并发</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="status-card">
          <div class="status-icon rate">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-value">{{ monitoringRate }}%</div>
            <div class="status-label">监控覆盖率</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 监控任务列表 -->
    <el-card class="monitor-card">
      <template #header>
        <div class="card-header">
          <span>监控任务</span>
          <div class="header-tools">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="已停止" value="stopped" />
              <el-option label="错误" value="error" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="filteredFolders"
        stripe
        class="monitor-table"
      >
        <el-table-column label="收藏夹信息" min-width="300">
          <template #default="{ row }">
            <div class="folder-info">
              <el-avatar :src="row.cover" :size="50" shape="square">
                <el-icon><Folder /></el-icon>
              </el-avatar>
              <div class="folder-details">
                <div class="folder-title">{{ row.title }}</div>
                <div class="folder-meta">
                  ID: {{ row.folderId }} | {{ row.videoCount }} 个视频
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="监控状态" width="120" align="center">
          <template #default="{ row }">
            <div class="status-indicator">
              <div class="status-dot" :class="getStatusClass(row.monitorStatus)"></div>
              <span>{{ getStatusText(row.monitorStatus) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="监控配置" width="150" align="center">
          <template #default="{ row }">
            <div class="monitor-config">
              <div>间隔: {{ row.monitorInterval || 60 }}分钟</div>
              <div>启用: {{ row.monitorEnabled ? '是' : '否' }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="最后监控" width="150" align="center">
          <template #default="{ row }">
            <div class="last-monitor">
              {{ row.lastMonitorTime ? formatTime.relative(row.lastMonitorTime) : '从未' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="失败次数" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.recentFailures > 3 ? 'danger' : row.recentFailures > 0 ? 'warning' : 'success'"
              size="small"
            >
              {{ row.recentFailures || 0 }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                :type="monitorStore.isMonitoring(row.folderId) ? 'danger' : 'success'"
                size="small"
                @click="toggleMonitor(row)"
                :loading="monitorStore.loading"
              >
                {{ monitorStore.isMonitoring(row.folderId) ? '停止' : '启动' }}
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="executeMonitor(row)"
                :disabled="!row.monitorEnabled"
              >
                立即执行
              </el-button>
              <el-dropdown @command="(cmd) => handleCommand(cmd, row)" trigger="click">
                <el-button size="small" :icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="config">配置</el-dropdown-item>
                    <el-dropdown-item command="logs">日志</el-dropdown-item>
                    <el-dropdown-item command="detail">详情</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="监控配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="currentConfigFolder"
        :model="configForm"
        :rules="configRules"
        ref="configFormRef"
        label-width="120px"
      >
        <el-form-item label="启用监控" prop="monitorEnabled">
          <el-switch v-model="configForm.monitorEnabled" />
        </el-form-item>
        <el-form-item label="监控间隔" prop="monitorInterval">
          <el-input-number
            v-model="configForm.monitorInterval"
            :min="5"
            :max="1440"
            :step="5"
            controls-position="right"
          />
          <span class="form-unit">分钟</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="configSaving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  View,
  VideoPlay,
  VideoPause,
  Refresh,
  Folder,
  Setting,
  TrendCharts,
  More
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { formatTime, getStatusText } from '@/utils'
import type { FolderInfo } from '@/types'

// 路由
const router = useRouter()

// 状态管理
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()

// 响应式状态
const loading = ref(false)
const statusFilter = ref('')
const configDialogVisible = ref(false)
const configSaving = ref(false)
const currentConfigFolder = ref<FolderInfo | null>(null)
const configFormRef = ref<FormInstance>()

// 配置表单
const configForm = ref({
  monitorEnabled: false,
  monitorInterval: 60
})

// 表单验证规则
const configRules: FormRules = {
  monitorInterval: [
    { required: true, message: '请输入监控间隔', trigger: 'blur' },
    { type: 'number', min: 5, max: 1440, message: '监控间隔必须在5-1440分钟之间', trigger: 'blur' }
  ]
}

// 计算属性
const filteredFolders = computed(() => {
  let folders = foldersStore.folders
  
  if (statusFilter.value) {
    folders = folders.filter(folder => {
      switch (statusFilter.value) {
        case 'running':
          return folder.monitorStatus === 'RUNNING'
        case 'stopped':
          return folder.monitorStatus === 'STOPPED'
        case 'error':
          return folder.monitorStatus === 'ERROR'
        default:
          return true
      }
    })
  }
  
  return folders
})

const monitoringRate = computed(() => {
  const total = foldersStore.totalFolders
  return total > 0 ? ((monitorStore.monitoringCount / total) * 100).toFixed(1) : '0'
})

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    await Promise.all([
      foldersStore.fetchFolders(),
      monitorStore.fetchMonitorStatus()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const toggleMonitor = async (folder: FolderInfo) => {
  try {
    await monitorStore.toggleMonitor(folder.folderId)
    ElMessage.success('操作成功')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  }
}

const executeMonitor = async (folder: FolderInfo) => {
  try {
    await monitorStore.executeMonitor(folder.folderId)
    ElMessage.success('监控任务已开始执行')
  } catch (error: any) {
    ElMessage.error(error.message || '执行失败')
  }
}

const startAllMonitors = async () => {
  try {
    const result = await monitorStore.startAllMonitors()
    ElMessage.success(`批量启动完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)
  } catch (error: any) {
    ElMessage.error(error.message || '批量启动失败')
  }
}

const stopAllMonitors = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有监控任务吗？',
      '批量停止确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await monitorStore.stopAllMonitors()
    ElMessage.success(`批量停止完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量停止失败')
    }
  }
}

const handleCommand = (command: string, folder: FolderInfo) => {
  switch (command) {
    case 'config':
      showConfigDialog(folder)
      break
    case 'logs':
      ElMessage.info('日志功能开发中')
      break
    case 'detail':
      router.push(`/folders/${folder.folderId}`)
      break
  }
}

const showConfigDialog = (folder: FolderInfo) => {
  currentConfigFolder.value = folder
  configForm.value = {
    monitorEnabled: folder.monitorEnabled || false,
    monitorInterval: folder.monitorInterval || 60
  }
  configDialogVisible.value = true
}

const saveConfig = async () => {
  if (!configFormRef.value || !currentConfigFolder.value) return
  
  try {
    await configFormRef.value.validate()
    configSaving.value = true
    
    await monitorStore.updateMonitorConfig(currentConfigFolder.value.folderId, configForm.value)
    
    ElMessage.success('配置保存成功')
    configDialogVisible.value = false
    
  } catch (error: any) {
    if (error.fields) {
      return
    }
    ElMessage.error(error.message || '配置保存失败')
  } finally {
    configSaving.value = false
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'RUNNING': return 'running'
    case 'STOPPED': return 'stopped'
    case 'ERROR': return 'error'
    default: return 'stopped'
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.monitor-page {
  padding: 0;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.header-left .page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 状态概览 */
.status-overview {
  margin-bottom: 24px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.status-icon.running {
  background: #67c23a20;
  color: #67c23a;
}

.status-icon.total {
  background: #409eff20;
  color: #409eff;
}

.status-icon.capacity {
  background: #e6a23c20;
  color: #e6a23c;
}

.status-icon.rate {
  background: #f56c6c20;
  color: #f56c6c;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* 收藏夹信息 */
.folder-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.folder-details {
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.running {
  background: #67c23a;
  animation: pulse 2s infinite;
}

.status-dot.stopped {
  background: #909399;
}

.status-dot.error {
  background: #f56c6c;
}

/* 监控配置 */
.monitor-config {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* 最后监控时间 */
.last-monitor {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 表单单位 */
.form-unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
    flex-wrap: wrap;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
