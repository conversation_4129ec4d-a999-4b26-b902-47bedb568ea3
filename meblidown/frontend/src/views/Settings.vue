<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Tools /></el-icon>
          系统设置
        </h1>
        <p class="page-subtitle">配置系统参数和用户偏好</p>
      </div>
    </div>

    <!-- 设置内容 -->
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :xs="24" :lg="6">
        <el-card class="settings-menu">
          <el-menu
            :default-active="activeTab"
            @select="handleMenuSelect"
            class="settings-nav"
          >
            <el-menu-item index="general">
              <el-icon><Setting /></el-icon>
              <span>常规设置</span>
            </el-menu-item>
            <el-menu-item index="monitor">
              <el-icon><View /></el-icon>
              <span>监控配置</span>
            </el-menu-item>
            <el-menu-item index="preload">
              <el-icon><Download /></el-icon>
              <span>预下载配置</span>
            </el-menu-item>
            <el-menu-item index="appearance">
              <el-icon><Brush /></el-icon>
              <span>外观设置</span>
            </el-menu-item>
            <el-menu-item index="about">
              <el-icon><InfoFilled /></el-icon>
              <span>关于系统</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :xs="24" :lg="18">
        <!-- 常规设置 -->
        <el-card v-show="activeTab === 'general'" class="settings-content">
          <template #header>
            <span>常规设置</span>
          </template>
          <el-form :model="generalForm" label-width="150px" class="settings-form">
            <el-form-item label="系统名称">
              <el-input v-model="generalForm.systemName" placeholder="MeBiliDown" />
            </el-form-item>
            <el-form-item label="数据刷新间隔">
              <el-input-number
                v-model="generalForm.refreshInterval"
                :min="10"
                :max="300"
                :step="10"
                controls-position="right"
              />
              <span class="form-unit">秒</span>
            </el-form-item>
            <el-form-item label="启用通知">
              <el-switch v-model="generalForm.enableNotifications" />
            </el-form-item>
            <el-form-item label="自动保存">
              <el-switch v-model="generalForm.autoSave" />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 监控配置 -->
        <el-card v-show="activeTab === 'monitor'" class="settings-content">
          <template #header>
            <span>监控配置</span>
          </template>
          <el-form :model="monitorForm" label-width="150px" class="settings-form">
            <el-form-item label="默认监控间隔">
              <el-input-number
                v-model="monitorForm.defaultInterval"
                :min="5"
                :max="1440"
                :step="5"
                controls-position="right"
              />
              <span class="form-unit">分钟</span>
            </el-form-item>
            <el-form-item label="最大并发数">
              <el-input-number
                v-model="monitorForm.maxConcurrent"
                :min="1"
                :max="10"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="失败重试次数">
              <el-input-number
                v-model="monitorForm.maxRetries"
                :min="0"
                :max="10"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="启用监控日志">
              <el-switch v-model="monitorForm.enableLogging" />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 预下载配置 -->
        <el-card v-show="activeTab === 'preload'" class="settings-content">
          <template #header>
            <span>预下载配置</span>
          </template>
          <el-form :model="preloadForm" label-width="150px" class="settings-form">
            <el-form-item label="默认预下载策略">
              <el-select v-model="preloadForm.defaultStrategy" style="width: 200px;">
                <el-option label="智能策略" value="smart" />
                <el-option label="全量策略" value="full" />
              </el-select>
            </el-form-item>
            <el-form-item label="默认预下载间隔">
              <el-input-number
                v-model="preloadForm.defaultInterval"
                :min="15"
                :max="1440"
                :step="15"
                controls-position="right"
              />
              <span class="form-unit">分钟</span>
            </el-form-item>
            <el-form-item label="最大并发数">
              <el-input-number
                v-model="preloadForm.maxConcurrent"
                :min="1"
                :max="10"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="CDN链接有效期">
              <el-input-number
                v-model="preloadForm.cdnExpireHours"
                :min="1"
                :max="24"
                controls-position="right"
              />
              <span class="form-unit">小时</span>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 外观设置 -->
        <el-card v-show="activeTab === 'appearance'" class="settings-content">
          <template #header>
            <span>外观设置</span>
          </template>
          <el-form :model="appearanceForm" label-width="150px" class="settings-form">
            <el-form-item label="主题模式">
              <el-radio-group v-model="appearanceForm.theme">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="主色调">
              <el-color-picker v-model="appearanceForm.primaryColor" />
            </el-form-item>
            <el-form-item label="侧边栏默认状态">
              <el-radio-group v-model="appearanceForm.sidebarCollapsed">
                <el-radio :label="false">展开</el-radio>
                <el-radio :label="true">折叠</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="动画效果">
              <el-switch v-model="appearanceForm.enableAnimations" />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 关于系统 -->
        <el-card v-show="activeTab === 'about'" class="settings-content">
          <template #header>
            <span>关于系统</span>
          </template>
          <div class="about-content">
            <div class="about-logo">
              <div class="logo-icon">
                <el-icon :size="64"><Monitor /></el-icon>
              </div>
              <h2>MeBiliDown</h2>
              <p>现代化B站收藏夹监控与预下载系统</p>
            </div>
            
            <div class="about-info">
              <div class="info-section">
                <h3>版本信息</h3>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">版本号:</span>
                    <span class="info-value">v1.0.0</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">构建时间:</span>
                    <span class="info-value">{{ buildTime }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Git提交:</span>
                    <span class="info-value">{{ gitCommit }}</span>
                  </div>
                </div>
              </div>

              <div class="info-section">
                <h3>技术栈</h3>
                <div class="tech-stack">
                  <el-tag>Vue 3</el-tag>
                  <el-tag>TypeScript</el-tag>
                  <el-tag>Element Plus</el-tag>
                  <el-tag>Vite</el-tag>
                  <el-tag>Spring Boot</el-tag>
                  <el-tag>Java</el-tag>
                </div>
              </div>

              <div class="info-section">
                <h3>功能特性</h3>
                <ul class="feature-list">
                  <li>实时监控B站收藏夹变化</li>
                  <li>智能预下载视频CDN链接</li>
                  <li>多策略预下载算法</li>
                  <li>现代化Web界面</li>
                  <li>WebSocket实时通信</li>
                  <li>响应式设计</li>
                </ul>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 保存按钮 -->
        <div class="settings-actions" v-if="activeTab !== 'about'">
          <el-button @click="resetSettings">重置</el-button>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Tools,
  Setting,
  View,
  Download,
  Brush,
  InfoFilled,
  Monitor
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式状态
const activeTab = ref('general')
const saving = ref(false)

// 表单数据
const generalForm = ref({
  systemName: 'MeBiliDown',
  refreshInterval: 30,
  enableNotifications: true,
  autoSave: true
})

const monitorForm = ref({
  defaultInterval: 60,
  maxConcurrent: 3,
  maxRetries: 3,
  enableLogging: true
})

const preloadForm = ref({
  defaultStrategy: 'smart',
  defaultInterval: 60,
  maxConcurrent: 2,
  cdnExpireHours: 6
})

const appearanceForm = ref({
  theme: 'light',
  primaryColor: '#409eff',
  sidebarCollapsed: false,
  enableAnimations: true
})

// 计算属性
const buildTime = computed(() => {
  return new Date().toLocaleString()
})

const gitCommit = computed(() => {
  return 'abc123f'
})

// 方法
const handleMenuSelect = (index: string) => {
  activeTab.value = index
}

const saveSettings = async () => {
  try {
    saving.value = true
    
    // 模拟保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存到本地存储
    const settings = {
      general: generalForm.value,
      monitor: monitorForm.value,
      preload: preloadForm.value,
      appearance: appearanceForm.value
    }
    
    localStorage.setItem('mebilidown-settings', JSON.stringify(settings))
    
    ElMessage.success('设置保存成功')
    
  } catch (error) {
    ElMessage.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  switch (activeTab.value) {
    case 'general':
      generalForm.value = {
        systemName: 'MeBiliDown',
        refreshInterval: 30,
        enableNotifications: true,
        autoSave: true
      }
      break
    case 'monitor':
      monitorForm.value = {
        defaultInterval: 60,
        maxConcurrent: 3,
        maxRetries: 3,
        enableLogging: true
      }
      break
    case 'preload':
      preloadForm.value = {
        defaultStrategy: 'smart',
        defaultInterval: 60,
        maxConcurrent: 2,
        cdnExpireHours: 6
      }
      break
    case 'appearance':
      appearanceForm.value = {
        theme: 'light',
        primaryColor: '#409eff',
        sidebarCollapsed: false,
        enableAnimations: true
      }
      break
  }
  
  ElMessage.success('设置已重置')
}
</script>

<style scoped>
.settings-page {
  padding: 0;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.header-left .page-subtitle {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 14px;
}

/* 设置菜单 */
.settings-menu {
  position: sticky;
  top: 20px;
}

.settings-nav {
  border: none;
}

.settings-nav .el-menu-item {
  border-radius: 6px;
  margin-bottom: 4px;
}

/* 设置内容 */
.settings-content {
  min-height: 400px;
}

.settings-form {
  max-width: 600px;
}

.form-unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 关于页面 */
.about-content {
  text-align: center;
}

.about-logo {
  margin-bottom: 40px;
}

.logo-icon {
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.about-logo h2 {
  font-size: 32px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.about-logo p {
  color: var(--el-text-color-secondary);
  margin: 0;
  font-size: 16px;
}

.about-info {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.info-section {
  margin-bottom: 32px;
}

.info-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.info-value {
  color: var(--el-text-color-primary);
  font-family: monospace;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  position: relative;
  padding-left: 20px;
}

.feature-list li:last-child {
  border-bottom: none;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--el-color-success);
  font-weight: bold;
}

/* 设置操作 */
.settings-actions {
  margin-top: 24px;
  text-align: right;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

/* 响应式 */
@media (max-width: 768px) {
  .settings-menu {
    position: static;
    margin-bottom: 20px;
  }

  .settings-nav {
    display: flex;
    overflow-x: auto;
  }

  .settings-nav .el-menu-item {
    white-space: nowrap;
    margin-right: 8px;
    margin-bottom: 0;
  }

  .settings-form {
    max-width: none;
  }

  .about-info {
    max-width: none;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .settings-actions {
    text-align: center;
  }
}
</style>
