<template>
  <div class="not-found">
    <div class="not-found-content">
      <!-- 404图标 -->
      <div class="error-icon">
        <el-icon :size="120">
          <WarningFilled />
        </el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-code">404</h1>
        <h2 class="error-title">页面不存在</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
          <br>
          请检查URL是否正确，或返回首页继续浏览。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" size="large" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button size="large" @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>

      <!-- 建议链接 -->
      <div class="suggested-links">
        <h3>您可能想要访问：</h3>
        <div class="links-grid">
          <router-link
            v-for="link in suggestedLinks"
            :key="link.path"
            :to="link.path"
            class="link-item"
          >
            <el-icon>
              <component :is="link.icon" />
            </el-icon>
            <span>{{ link.title }}</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  WarningFilled,
  HomeFilled,
  Back,
  Monitor,
  Folder,
  View,
  Download,
  Setting
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 建议的链接
const suggestedLinks = [
  {
    path: '/dashboard',
    title: '仪表板',
    icon: Monitor
  },
  {
    path: '/folders',
    title: '收藏夹管理',
    icon: Folder
  },
  {
    path: '/monitor',
    title: '监控管理',
    icon: View
  },
  {
    path: '/preload',
    title: '预下载管理',
    icon: Download
  },
  {
    path: '/system',
    title: '系统状态',
    icon: Setting
  }
]

// 方法
const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/dashboard')
  }
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.not-found-content {
  text-align: center;
  z-index: 2;
  position: relative;
  max-width: 600px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 错误图标 */
.error-icon {
  margin-bottom: 30px;
  color: #f56c6c;
  animation: bounce 2s infinite;
}

/* 错误信息 */
.error-code {
  font-size: 72px;
  font-weight: 900;
  color: #409eff;
  margin: 0 0 16px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin: 0 0 40px 0;
}

/* 操作按钮 */
.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
}

/* 建议链接 */
.suggested-links h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 20px 0;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.link-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 12px;
  background: var(--el-fill-color-lighter);
  color: var(--el-text-color-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.link-item:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.2);
}

.link-item .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.link-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .not-found-content {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .error-code {
    font-size: 56px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-description {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 48px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .link-item {
    padding: 12px;
  }
}
</style>
