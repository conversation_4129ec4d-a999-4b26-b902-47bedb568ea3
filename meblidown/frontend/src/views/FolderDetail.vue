<template>
  <div class="folder-detail">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button :icon="ArrowLeft" @click="goBack">返回列表</el-button>
    </div>

    <!-- 收藏夹信息 -->
    <el-card v-if="currentFolder" class="folder-info-card">
      <div class="folder-header">
        <el-avatar :src="currentFolder.cover" :size="80" shape="square">
          <el-icon><Folder /></el-icon>
        </el-avatar>
        <div class="folder-details">
          <h1 class="folder-title">{{ currentFolder.title }}</h1>
          <p class="folder-description">{{ currentFolder.description || '暂无描述' }}</p>
          <div class="folder-meta">
            <span>ID: {{ currentFolder.folderId }}</span>
            <el-divider direction="vertical" />
            <span>{{ currentFolder.videoCount }} 个视频</span>
            <el-divider direction="vertical" />
            <span>创建者: {{ currentFolder.creator?.name || '未知' }}</span>
          </div>
        </div>
        <div class="folder-actions">
          <el-button
            :type="monitorStore.isMonitoring(folderId) ? 'danger' : 'success'"
            @click="toggleMonitor"
            :loading="monitorStore.loading"
          >
            {{ monitorStore.isMonitoring(folderId) ? '停止监控' : '开始监控' }}
          </el-button>
          <el-button
            :type="preloadStore.isPreloading(folderId) ? 'danger' : 'primary'"
            @click="togglePreload"
            :loading="preloadStore.loading"
          >
            {{ preloadStore.isPreloading(folderId) ? '停止预下载' : '开始预下载' }}
          </el-button>
          <el-button :icon="Refresh" @click="refreshFolder">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: #409eff20; color: #409eff;">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ currentFolder?.videoCount || 0 }}</div>
            <div class="stat-label">总视频数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: #67c23a20; color: #67c23a;">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ currentFolder?.validVideoCount || 0 }}</div>
            <div class="stat-label">有效视频</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: #f56c6c20; color: #f56c6c;">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ currentFolder?.invalidVideoCount || 0 }}</div>
            <div class="stat-label">失效视频</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon" style="background: #e6a23c20; color: #e6a23c;">
            <el-icon><Download /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ preloadCount }}</div>
            <div class="stat-label">预下载数</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 视频列表 -->
    <el-card class="videos-card">
      <template #header>
        <div class="card-header">
          <span>视频列表</span>
          <div class="header-tools">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="有效" value="valid" />
              <el-option label="失效" value="invalid" />
            </el-select>
            <el-input
              v-model="searchText"
              placeholder="搜索视频..."
              :prefix-icon="Search"
              clearable
              style="width: 200px;"
            />
          </div>
        </div>
      </template>

      <el-table
        v-loading="videosLoading"
        :data="folderVideos?.items || []"
        stripe
        class="videos-table"
      >
        <el-table-column label="视频信息" min-width="400">
          <template #default="{ row }">
            <div class="video-info">
              <el-avatar :src="row.cover" :size="60" shape="square">
                <el-icon><VideoPlay /></el-icon>
              </el-avatar>
              <div class="video-details">
                <div class="video-title">{{ row.title }}</div>
                <div class="video-meta">
                  <span>{{ row.bvid }}</span>
                  <el-divider direction="vertical" />
                  <span>{{ formatDuration(row.duration) }}</span>
                  <el-divider direction="vertical" />
                  <span>{{ row.uploader?.name || '未知UP主' }}</span>
                </div>
                <div class="video-stats" v-if="row.stats">
                  <span>{{ formatNumber(row.stats.playCount) }} 播放</span>
                  <span>{{ formatNumber(row.stats.likeCount) }} 点赞</span>
                  <span>{{ formatNumber(row.stats.coinCount) }} 投币</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.valid ? 'success' : 'danger'" size="small">
              {{ row.valid ? '有效' : '失效' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="收藏时间" width="150" align="center">
          <template #default="{ row }">
            <div class="time-text">
              {{ formatTime.relative(row.favoriteTime) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="openVideo(row.bvid)"
            >
              打开视频
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="folderVideos">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="folderVideos.total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeft,
  Folder,
  VideoPlay,
  SuccessFilled,
  WarningFilled,
  Download,
  Refresh,
  Search
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { formatTime, formatNumber, formatDuration } from '@/utils'

// 路由
const route = useRoute()
const router = useRouter()

// 状态管理
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 响应式状态
const currentPage = ref(1)
const pageSize = ref(20)
const statusFilter = ref('')
const searchText = ref('')
const preloadCount = ref(0)

// 计算属性
const folderId = computed(() => parseInt(route.params.id as string))
const currentFolder = computed(() => foldersStore.currentFolder)
const folderVideos = computed(() => foldersStore.folderVideos)
const videosLoading = computed(() => foldersStore.videosLoading)

// 方法
const goBack = () => {
  router.push('/folders')
}

const toggleMonitor = async () => {
  try {
    await monitorStore.toggleMonitor(folderId.value)
    ElMessage.success('操作成功')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  }
}

const togglePreload = async () => {
  try {
    await preloadStore.togglePreload(folderId.value)
    ElMessage.success('操作成功')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  }
}

const refreshFolder = async () => {
  try {
    await foldersStore.refreshFolder(folderId.value)
    await loadVideos()
    ElMessage.success('刷新成功')
  } catch (error: any) {
    ElMessage.error(error.message || '刷新失败')
  }
}

const loadVideos = async () => {
  try {
    await foldersStore.fetchFolderVideos(folderId.value, {
      page: currentPage.value,
      size: pageSize.value,
      status: statusFilter.value as any
    })
  } catch (error: any) {
    ElMessage.error(error.message || '加载视频失败')
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadVideos()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadVideos()
}

const openVideo = (bvid: string) => {
  window.open(`https://www.bilibili.com/video/${bvid}`, '_blank')
}

// 监听筛选条件变化
watch([statusFilter, searchText], () => {
  currentPage.value = 1
  loadVideos()
})

onMounted(async () => {
  try {
    // 加载收藏夹详情
    await foldersStore.fetchFolderById(folderId.value)
    
    // 加载视频列表
    await loadVideos()
    
    // 加载预下载统计
    try {
      const stats = await preloadStore.fetchPreloadStats(folderId.value)
      preloadCount.value = stats.totalPreloads || 0
    } catch (error) {
      console.warn('加载预下载统计失败:', error)
    }
    
  } catch (error: any) {
    ElMessage.error(error.message || '加载数据失败')
    router.push('/folders')
  }
})
</script>

<style scoped>
.folder-detail {
  padding: 0;
}

/* 返回按钮 */
.back-button {
  margin-bottom: 20px;
}

/* 收藏夹信息卡片 */
.folder-info-card {
  margin-bottom: 20px;
}

.folder-header {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.folder-details {
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-description {
  color: var(--el-text-color-secondary);
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.folder-meta {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.folder-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.header-tools {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 视频信息 */
.video-info {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.video-stats {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  gap: 12px;
}

.time-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 响应式 */
@media (max-width: 768px) {
  .folder-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .folder-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .header-tools {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .video-info {
    gap: 8px;
  }
  
  .video-stats {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
