// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: string
}

// 分页响应类型
export interface PageResponse<T = any> {
  page: number
  size: number
  total: number
  totalPages: number
  items: T[]
  hasNext: boolean
  hasPrevious: boolean
}

// 收藏夹信息
export interface FolderInfo {
  folderId: number
  title: string
  description?: string
  cover?: string
  creator?: UploaderInfo
  videoCount: number
  validVideoCount: number
  invalidVideoCount: number
  isPublic: boolean
  monitorEnabled: boolean
  monitorInterval: number
  preloadEnabled: boolean
  preloadStrategy: string
  preloadInterval: number
  lastMonitorTime?: string
  lastPreloadTime?: string
  recentFailures: number
  monitorStatus: 'STOPPED' | 'RUNNING' | 'PAUSED' | 'ERROR'
  preloadStatus: 'STOPPED' | 'RUNNING' | 'PAUSED' | 'ERROR'
  createTime: string
  updateTime: string
}

// UP主信息
export interface UploaderInfo {
  mid: number
  name: string
  face?: string
  level?: number
  verified: boolean
}

// 视频信息
export interface VideoInfo {
  bvid: string
  aid: string
  title: string
  description?: string
  cover?: string
  duration?: number
  uploader?: UploaderInfo
  stats?: VideoStats
  publishTime?: string
  favoriteTime?: string
  status: 'VALID' | 'INVALID' | 'DELETED' | 'PRIVATE' | 'PROCESSING'
  folderId: number
  cid?: number
  quality?: number
  valid: boolean
  createTime: string
  updateTime: string
}

// 视频统计信息
export interface VideoStats {
  playCount: number
  danmakuCount: number
  replyCount: number
  favoriteCount: number
  coinCount: number
  shareCount: number
  likeCount: number
}

// 预下载信息
export interface PreloadInfo {
  bvid: string
  aid: string
  title: string
  folderId: number
  cid?: number
  quality?: number
  videoUrl?: string
  audioUrl?: string
  status: 'PENDING' | 'VALID' | 'EXPIRING_SOON' | 'EXPIRED' | 'FAILED' | 'REFRESHING'
  cdnValid: boolean
  cdnObtainTime?: string
  cdnExpireTime?: string
  retryCount: number
  maxRetries: number
  createTime: string
  updateTime: string
}

// 系统状态
export interface SystemStatus {
  memory: {
    used: number
    free: number
    max: number
    usagePercent: number
  }
  storage: {
    folderCount: number
    videoCount: number
    preloadCount: number
    validVideoCount: number
    invalidVideoCount: number
    validPreloadCount: number
    expiredPreloadCount: number
  }
  monitor: {
    enabled: boolean
    monitoringCount: number
    maxConcurrent: number
    monitoringFolders: number[]
  }
  websocket: {
    totalConnections: number
    activeConnections: number
  }
  healthy: boolean
  timestamp: string
}

// WebSocket消息
export interface WebSocketMessage {
  type: string
  data: any
  message: string
  timestamp: number
}

// 监控事件
export interface MonitorEvent {
  type: 'monitor_status' | 'new_videos' | 'invalid_videos' | 'preload_status' | 'preload_progress' | 'error' | 'success'
  folderId?: number
  data: any
  message: string
  timestamp: number
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  align?: 'left' | 'center' | 'right'
}

// 统计卡片
export interface StatCard {
  title: string
  value: string | number
  icon: string
  color: string
  trend?: {
    value: number
    isUp: boolean
  }
}

// 图表数据
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
    fill?: boolean
  }[]
}

// 表单规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}
