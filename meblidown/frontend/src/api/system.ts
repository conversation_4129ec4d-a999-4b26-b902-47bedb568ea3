import { request } from './index'
import type { SystemStatus } from '@/types'

// 系统API
export const systemApi = {
  // 获取系统基本信息
  getInfo(): Promise<any> {
    return request.get<any>('/system/info').then(res => res.data)
  },

  // 获取系统状态
  getStatus(): Promise<SystemStatus> {
    return request.get<SystemStatus>('/system/status').then(res => res.data)
  },

  // 获取系统健康检查
  getHealth(): Promise<any> {
    return request.get<any>('/system/health').then(res => res.data)
  },

  // 获取系统统计信息
  getStats(): Promise<any> {
    return request.get<any>('/system/stats').then(res => res.data)
  },

  // 获取系统配置
  getConfig(): Promise<any> {
    return request.get<any>('/system/config').then(res => res.data)
  },

  // 触发垃圾回收
  triggerGC(): Promise<any> {
    return request.post<any>('/system/gc').then(res => res.data)
  }
}
