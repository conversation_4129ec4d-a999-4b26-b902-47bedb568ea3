import { request } from './index'
import type { FolderInfo, VideoInfo, PageResponse } from '@/types'

// 收藏夹API
export const foldersApi = {
  // 获取所有收藏夹
  getAll(): Promise<FolderInfo[]> {
    return request.get<FolderInfo[]>('/folders').then(res => res.data)
  },

  // 根据ID获取收藏夹
  getById(folderId: number): Promise<FolderInfo> {
    return request.get<FolderInfo>(`/folders/${folderId}`).then(res => res.data)
  },

  // 添加收藏夹
  add(folderId: number): Promise<FolderInfo> {
    return request.post<FolderInfo>(`/folders/${folderId}`).then(res => res.data)
  },

  // 更新收藏夹配置
  update(folderId: number, data: Partial<FolderInfo>): Promise<FolderInfo> {
    return request.put<FolderInfo>(`/folders/${folderId}`, data).then(res => res.data)
  },

  // 删除收藏夹
  delete(folderId: number): Promise<void> {
    return request.delete<void>(`/folders/${folderId}`).then(res => res.data)
  },

  // 获取收藏夹视频列表
  getVideos(
    folderId: number, 
    params: {
      page?: number
      size?: number
      status?: 'valid' | 'invalid'
    } = {}
  ): Promise<PageResponse<VideoInfo>> {
    const { page = 1, size = 20, status } = params
    const query = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...(status && { status })
    })
    return request.get<PageResponse<VideoInfo>>(`/folders/${folderId}/videos?${query}`).then(res => res.data)
  },

  // 刷新收藏夹信息
  refresh(folderId: number): Promise<FolderInfo> {
    return request.post<FolderInfo>(`/folders/${folderId}/refresh`).then(res => res.data)
  },

  // 获取收藏夹统计信息
  getStats(folderId: number): Promise<any> {
    return request.get<any>(`/folders/${folderId}/stats`).then(res => res.data)
  }
}
