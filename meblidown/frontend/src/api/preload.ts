import { request } from './index'
import type { PreloadInfo, PageResponse, FolderInfo } from '@/types'

// 预下载API
export const preloadApi = {
  // 获取预下载状态
  getStatus(): Promise<any> {
    return request.get<any>('/preload/status').then(res => res.data)
  },

  // 启动收藏夹预下载
  start(folderId: number): Promise<void> {
    return request.post<void>(`/preload/folders/${folderId}/start`).then(res => res.data)
  },

  // 停止收藏夹预下载
  stop(folderId: number): Promise<void> {
    return request.post<void>(`/preload/folders/${folderId}/stop`).then(res => res.data)
  },

  // 立即执行预下载任务
  execute(folderId: number): Promise<void> {
    return request.post<void>(`/preload/folders/${folderId}/execute`).then(res => res.data)
  },

  // 获取收藏夹预下载信息
  getFolderPreloads(
    folderId: number,
    params: {
      page?: number
      size?: number
      status?: 'valid' | 'expired' | 'expiring' | 'failed'
    } = {}
  ): Promise<PageResponse<PreloadInfo>> {
    const { page = 1, size = 20, status } = params
    const query = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...(status && { status })
    })
    return request.get<PageResponse<PreloadInfo>>(`/preload/folders/${folderId}?${query}`).then(res => res.data)
  },

  // 获取预下载统计信息
  getStats(folderId: number): Promise<any> {
    return request.get<any>(`/preload/folders/${folderId}/stats`).then(res => res.data)
  },

  // 刷新过期的CDN链接
  refreshExpired(): Promise<void> {
    return request.post<void>('/preload/refresh-expired').then(res => res.data)
  },

  // 更新预下载配置
  updateConfig(folderId: number, config: {
    preloadEnabled?: boolean
    preloadStrategy?: string
    preloadInterval?: number
  }): Promise<FolderInfo> {
    return request.put<FolderInfo>(`/preload/folders/${folderId}/config`, config).then(res => res.data)
  },

  // 批量启动预下载
  startAll(): Promise<any> {
    return request.post<any>('/preload/start-all').then(res => res.data)
  },

  // 批量停止预下载
  stopAll(): Promise<any> {
    return request.post<any>('/preload/stop-all').then(res => res.data)
  }
}
