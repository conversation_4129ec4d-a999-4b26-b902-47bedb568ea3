import { request } from './index'
import type { FolderInfo } from '@/types'

// 监控API
export const monitorApi = {
  // 获取监控状态
  getStatus(): Promise<any> {
    return request.get<any>('/monitor/status').then(res => res.data)
  },

  // 启动收藏夹监控
  start(folderId: number): Promise<void> {
    return request.post<void>(`/monitor/folders/${folderId}/start`).then(res => res.data)
  },

  // 停止收藏夹监控
  stop(folderId: number): Promise<void> {
    return request.post<void>(`/monitor/folders/${folderId}/stop`).then(res => res.data)
  },

  // 立即执行监控任务
  execute(folderId: number): Promise<void> {
    return request.post<void>(`/monitor/folders/${folderId}/execute`).then(res => res.data)
  },

  // 批量启动监控
  startAll(): Promise<any> {
    return request.post<any>('/monitor/start-all').then(res => res.data)
  },

  // 批量停止监控
  stopAll(): Promise<any> {
    return request.post<any>('/monitor/stop-all').then(res => res.data)
  },

  // 更新监控配置
  updateConfig(folderId: number, config: {
    monitorEnabled?: boolean
    monitorInterval?: number
  }): Promise<FolderInfo> {
    return request.put<FolderInfo>(`/monitor/folders/${folderId}/config`, config).then(res => res.data)
  },

  // 获取监控日志
  getLogs(limit: number = 50): Promise<any> {
    return request.get<any>(`/monitor/logs?limit=${limit}`).then(res => res.data)
  }
}
