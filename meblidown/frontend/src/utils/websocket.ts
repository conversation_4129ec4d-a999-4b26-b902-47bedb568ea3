import { ElMessage, ElNotification } from 'element-plus'
import type { WebSocketMessage } from '@/types'

export class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private listeners: Map<string, Function[]> = new Map()

  constructor(private url: string = 'ws://localhost:3000/ws') {}

  // 连接WebSocket
  connect(): void {
    try {
      this.ws = new WebSocket(this.url)
      
      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.scheduleReconnect()
    }
  }

  // 断开连接
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  // 发送消息
  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(typeof message === 'string' ? message : JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  // 添加事件监听器
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  // 移除事件监听器
  off(event: string, callback?: Function): void {
    if (!this.listeners.has(event)) return
    
    if (callback) {
      const callbacks = this.listeners.get(event)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.listeners.delete(event)
    }
  }

  // 触发事件
  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket事件处理错误:', error)
        }
      })
    }
  }

  // 连接成功
  private onOpen(): void {
    console.log('WebSocket连接成功')
    this.reconnectAttempts = 0
    
    // 发送心跳
    this.send('ping')
    
    this.emit('connected', null)
  }

  // 接收消息
  private onMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      console.log('收到WebSocket消息:', message)
      
      // 处理不同类型的消息
      this.handleMessage(message)
      
      // 触发通用消息事件
      this.emit('message', message)
      
      // 触发特定类型事件
      this.emit(message.type, message)
      
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  // 连接关闭
  private onClose(event: CloseEvent): void {
    console.log('WebSocket连接关闭:', event.code, event.reason)
    this.ws = null
    
    this.emit('disconnected', { code: event.code, reason: event.reason })
    
    // 如果不是主动关闭，尝试重连
    if (event.code !== 1000) {
      this.scheduleReconnect()
    }
  }

  // 连接错误
  private onError(event: Event): void {
    console.error('WebSocket连接错误:', event)
    this.emit('error', event)
  }

  // 处理消息
  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'connection':
        ElMessage.success('实时连接已建立')
        break
        
      case 'monitor_status':
        this.handleMonitorStatus(message)
        break
        
      case 'new_videos':
        this.handleNewVideos(message)
        break
        
      case 'invalid_videos':
        this.handleInvalidVideos(message)
        break
        
      case 'preload_status':
        this.handlePreloadStatus(message)
        break
        
      case 'preload_progress':
        this.handlePreloadProgress(message)
        break
        
      case 'error':
        ElMessage.error(message.message || '系统错误')
        break
        
      case 'success':
        ElMessage.success(message.message || '操作成功')
        break
        
      default:
        console.log('未处理的消息类型:', message.type)
    }
  }

  // 处理监控状态变化
  private handleMonitorStatus(message: WebSocketMessage): void {
    const { folderId, status } = message.data
    const statusText = status === 'STARTED' ? '已启动' : '已停止'
    
    ElNotification({
      title: '监控状态变化',
      message: `收藏夹 ${folderId} 监控${statusText}`,
      type: status === 'STARTED' ? 'success' : 'info',
      duration: 3000
    })
  }

  // 处理新视频通知
  private handleNewVideos(message: WebSocketMessage): void {
    const { folderId, count } = message.data
    
    ElNotification({
      title: '发现新视频',
      message: `收藏夹 ${folderId} 发现 ${count} 个新视频`,
      type: 'success',
      duration: 5000
    })
  }

  // 处理失效视频通知
  private handleInvalidVideos(message: WebSocketMessage): void {
    const { folderId, count } = message.data
    
    ElNotification({
      title: '视频失效',
      message: `收藏夹 ${folderId} 发现 ${count} 个失效视频`,
      type: 'warning',
      duration: 5000
    })
  }

  // 处理预下载状态变化
  private handlePreloadStatus(message: WebSocketMessage): void {
    const { folderId, status } = message.data
    const statusText = status === 'STARTED' ? '已启动' : '已停止'
    
    ElNotification({
      title: '预下载状态变化',
      message: `收藏夹 ${folderId} 预下载${statusText}`,
      type: status === 'STARTED' ? 'success' : 'info',
      duration: 3000
    })
  }

  // 处理预下载进度
  private handlePreloadProgress(message: WebSocketMessage): void {
    const { folderId, completed, total, progress } = message.data
    
    // 这里可以更新进度条或其他UI组件
    this.emit('preload_progress_update', { folderId, completed, total, progress })
  }

  // 安排重连
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限')
      ElMessage.error('实时连接失败，请刷新页面重试')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  // 获取连接状态
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  // 获取连接状态文本
  get connectionStatus(): string {
    if (!this.ws) return '未连接'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return '连接中'
      case WebSocket.OPEN:
        return '已连接'
      case WebSocket.CLOSING:
        return '断开中'
      case WebSocket.CLOSED:
        return '已断开'
      default:
        return '未知状态'
    }
  }
}

// 创建全局WebSocket实例
export const wsService = new WebSocketService()
