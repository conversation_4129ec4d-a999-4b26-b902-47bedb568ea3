import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 格式化时间
export const formatTime = {
  // 格式化为相对时间
  relative(time: string | Date): string {
    return dayjs(time).fromNow()
  },

  // 格式化为标准时间
  standard(time: string | Date): string {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
  },

  // 格式化为日期
  date(time: string | Date): string {
    return dayjs(time).format('YYYY-MM-DD')
  },

  // 格式化为时间
  time(time: string | Date): string {
    return dayjs(time).format('HH:mm:ss')
  }
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化数字
export function formatNumber(num: number): string {
  if (num < 1000) return num.toString()
  if (num < 10000) return (num / 1000).toFixed(1) + 'K'
  if (num < 100000000) return (num / 10000).toFixed(1) + '万'
  return (num / 100000000).toFixed(1) + '亿'
}

// 格式化时长（秒转为时分秒）
export function formatDuration(seconds: number): string {
  if (!seconds || seconds < 0) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

// 格式化百分比
export function formatPercent(value: number, total: number): string {
  if (total === 0) return '0%'
  return ((value / total) * 100).toFixed(1) + '%'
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  
  return function (...args: Parameters<T>) {
    if (timeout !== null) {
      clearTimeout(timeout)
    }
    timeout = window.setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 复制到剪贴板
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      return true
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}

// 下载文件
export function downloadFile(url: string, filename?: string): void {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 获取状态颜色
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    'RUNNING': '#67c23a',
    'STOPPED': '#909399',
    'PAUSED': '#e6a23c',
    'ERROR': '#f56c6c',
    'VALID': '#67c23a',
    'INVALID': '#f56c6c',
    'EXPIRED': '#f56c6c',
    'EXPIRING_SOON': '#e6a23c',
    'PENDING': '#909399',
    'FAILED': '#f56c6c'
  }
  return statusColors[status] || '#909399'
}

// 获取状态文本
export function getStatusText(status: string): string {
  const statusTexts: Record<string, string> = {
    'RUNNING': '运行中',
    'STOPPED': '已停止',
    'PAUSED': '已暂停',
    'ERROR': '错误',
    'VALID': '有效',
    'INVALID': '失效',
    'EXPIRED': '已过期',
    'EXPIRING_SOON': '即将过期',
    'PENDING': '等待中',
    'FAILED': '失败',
    'REFRESHING': '刷新中'
  }
  return statusTexts[status] || status
}

// 生成随机ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as any
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any
  if (typeof obj === 'object') {
    const clonedObj: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// 检查是否为空值
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

// 安全的JSON解析
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str)
  } catch {
    return defaultValue
  }
}

// 获取URL参数
export function getUrlParams(): Record<string, string> {
  const params: Record<string, string> = {}
  const searchParams = new URLSearchParams(window.location.search)
  
  for (const [key, value] of searchParams) {
    params[key] = value
  }
  
  return params
}

// 设置页面标题
export function setPageTitle(title: string): void {
  document.title = title ? `${title} - MeBiliDown` : 'MeBiliDown'
}
