<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <AppLayout />
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import AppLayout from '@/components/layout/AppLayout.vue'
import { useWebSocketStore } from '@/stores/websocket'
import { useSystemStore } from '@/stores/system'
import { useFoldersStore } from '@/stores/folders'

// Element Plus 本地化
const locale = zhCn

// 状态管理
const wsStore = useWebSocketStore()
const systemStore = useSystemStore()
const foldersStore = useFoldersStore()

// 清理函数
let cleanupFunctions: (() => void)[] = []

onMounted(async () => {
  console.log('🚀 MeBiliDown 应用启动')
  
  try {
    // 初始化WebSocket连接
    wsStore.initialize()
    
    // 启动心跳
    const stopHeartbeat = wsStore.startHeartbeat()
    cleanupFunctions.push(stopHeartbeat)
    
    // 加载初始数据
    await Promise.all([
      systemStore.fetchAllData(),
      foldersStore.fetchFolders()
    ])
    
    // 启动自动刷新
    const stopSystemRefresh = systemStore.startAutoRefresh()
    cleanupFunctions.push(stopSystemRefresh)
    
    console.log('✅ 应用初始化完成')
    
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
})

onUnmounted(() => {
  console.log('🔄 清理应用资源')
  
  // 执行所有清理函数
  cleanupFunctions.forEach(cleanup => {
    try {
      cleanup()
    } catch (error) {
      console.error('清理函数执行失败:', error)
    }
  })
  
  // 断开WebSocket连接
  wsStore.disconnect()
  
  console.log('✅ 应用资源清理完成')
})
</script>

<style>
#app {
  height: 100vh;
  overflow: hidden;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式断点 */
@media (max-width: 768px) {
  #app {
    height: 100vh;
  }
}
</style>
