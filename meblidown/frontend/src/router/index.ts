import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      icon: 'Monitor'
    }
  },
  {
    path: '/folders',
    name: 'Folders',
    component: () => import('@/views/Folders.vue'),
    meta: {
      title: '收藏夹管理',
      icon: 'Folder'
    }
  },
  {
    path: '/folders/:id',
    name: 'FolderDetail',
    component: () => import('@/views/FolderDetail.vue'),
    meta: {
      title: '收藏夹详情',
      icon: 'Document'
    }
  },
  {
    path: '/monitor',
    name: 'Monitor',
    component: () => import('@/views/Monitor.vue'),
    meta: {
      title: '监控管理',
      icon: 'View'
    }
  },
  {
    path: '/preload',
    name: 'Preload',
    component: () => import('@/views/Preload.vue'),
    meta: {
      title: '预下载管理',
      icon: 'Download'
    }
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/views/System.vue'),
    meta: {
      title: '系统状态',
      icon: 'Setting'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: '系统设置',
      icon: 'Tools'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta?.title as string
  if (title) {
    document.title = `${title} - MeBiliDown`
  } else {
    document.title = 'MeBiliDown'
  }
  
  next()
})

router.afterEach(() => {
  // 完成进度条
  NProgress.done()
})

router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
