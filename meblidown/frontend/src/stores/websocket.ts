import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { wsService } from '@/utils/websocket'
import { useMonitorStore } from './monitor'
import { usePreloadStore } from './preload'
import { useSystemStore } from './system'
import type { WebSocketMessage } from '@/types'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const isConnected = ref(false)
  const connectionStatus = ref('未连接')
  const lastMessage = ref<WebSocketMessage | null>(null)
  const messageHistory = ref<WebSocketMessage[]>([])
  const reconnectAttempts = ref(0)
  const maxHistorySize = 100

  // 获取其他stores
  const monitorStore = useMonitorStore()
  const preloadStore = usePreloadStore()
  const systemStore = useSystemStore()

  // 计算属性
  const connectionStatusColor = computed(() => {
    switch (connectionStatus.value) {
      case '已连接':
        return '#67c23a'
      case '连接中':
        return '#e6a23c'
      case '已断开':
      case '未连接':
        return '#f56c6c'
      default:
        return '#909399'
    }
  })

  const recentMessages = computed(() => {
    return messageHistory.value.slice(-10).reverse()
  })

  // 方法
  const connect = () => {
    try {
      wsService.connect()
      setupEventListeners()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
    }
  }

  const disconnect = () => {
    wsService.disconnect()
    isConnected.value = false
    connectionStatus.value = '已断开'
  }

  const setupEventListeners = () => {
    // 连接状态事件
    wsService.on('connected', () => {
      isConnected.value = true
      connectionStatus.value = '已连接'
      reconnectAttempts.value = 0
    })

    wsService.on('disconnected', () => {
      isConnected.value = false
      connectionStatus.value = '已断开'
    })

    wsService.on('error', () => {
      connectionStatus.value = '连接错误'
    })

    // 消息事件
    wsService.on('message', (message: WebSocketMessage) => {
      lastMessage.value = message
      addToHistory(message)
      handleMessage(message)
    })

    // 监控相关事件
    wsService.on('monitor_status', (message: WebSocketMessage) => {
      const { folderId, status } = message.data
      monitorStore.handleMonitorStatusChange(folderId, status)
    })

    wsService.on('new_videos', (message: WebSocketMessage) => {
      // 可以在这里处理新视频通知
      console.log('收到新视频通知:', message.data)
    })

    wsService.on('invalid_videos', (message: WebSocketMessage) => {
      // 可以在这里处理失效视频通知
      console.log('收到失效视频通知:', message.data)
    })

    // 预下载相关事件
    wsService.on('preload_status', (message: WebSocketMessage) => {
      const { folderId, status } = message.data
      preloadStore.handlePreloadStatusChange(folderId, status)
    })

    wsService.on('preload_progress', (message: WebSocketMessage) => {
      preloadStore.handlePreloadProgress(message.data)
    })

    // 系统相关事件
    wsService.on('system_status', (message: WebSocketMessage) => {
      // 可以在这里处理系统状态更新
      console.log('收到系统状态更新:', message.data)
    })
  }

  const handleMessage = (message: WebSocketMessage) => {
    // 根据消息类型进行特殊处理
    switch (message.type) {
      case 'connection':
        console.log('WebSocket连接确认:', message.message)
        break
      
      case 'error':
        console.error('WebSocket错误消息:', message.message)
        break
      
      case 'success':
        console.log('WebSocket成功消息:', message.message)
        break
      
      default:
        console.log('WebSocket消息:', message)
    }
  }

  const addToHistory = (message: WebSocketMessage) => {
    messageHistory.value.push(message)
    
    // 限制历史记录大小
    if (messageHistory.value.length > maxHistorySize) {
      messageHistory.value = messageHistory.value.slice(-maxHistorySize)
    }
  }

  const sendMessage = (message: any) => {
    if (isConnected.value) {
      wsService.send(message)
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  const sendPing = () => {
    sendMessage('ping')
  }

  const clearHistory = () => {
    messageHistory.value = []
  }

  const getConnectionInfo = () => {
    return {
      isConnected: isConnected.value,
      status: connectionStatus.value,
      statusColor: connectionStatusColor.value,
      reconnectAttempts: reconnectAttempts.value,
      messageCount: messageHistory.value.length,
      lastMessageTime: lastMessage.value?.timestamp
    }
  }

  const startHeartbeat = (interval: number = 30000) => {
    // 每30秒发送一次心跳
    const timer = setInterval(() => {
      if (isConnected.value) {
        sendPing()
      }
    }, interval)

    // 返回清理函数
    return () => clearInterval(timer)
  }

  // 初始化连接
  const initialize = () => {
    connect()
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && !isConnected.value) {
        // 页面变为可见且未连接时，尝试重连
        setTimeout(() => {
          if (!isConnected.value) {
            connect()
          }
        }, 1000)
      }
    })
  }

  return {
    // 状态
    isConnected,
    connectionStatus,
    lastMessage,
    messageHistory,
    reconnectAttempts,

    // 计算属性
    connectionStatusColor,
    recentMessages,

    // 方法
    connect,
    disconnect,
    sendMessage,
    sendPing,
    clearHistory,
    getConnectionInfo,
    startHeartbeat,
    initialize
  }
})
