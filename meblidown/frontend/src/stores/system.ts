import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { systemApi } from '@/api/system'
import type { SystemStatus } from '@/types'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const systemInfo = ref<any>(null)
  const systemStatus = ref<SystemStatus | null>(null)
  const systemHealth = ref<any>(null)
  const systemStats = ref<any>(null)
  const systemConfig = ref<any>(null)
  const loading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)

  // 计算属性
  const isHealthy = computed(() => {
    return systemHealth.value?.overall === true
  })

  const memoryUsage = computed(() => {
    if (!systemStatus.value?.memory) return 0
    return systemStatus.value.memory.usagePercent
  })

  const memoryUsageColor = computed(() => {
    const usage = memoryUsage.value
    if (usage < 50) return '#67c23a'
    if (usage < 80) return '#e6a23c'
    return '#f56c6c'
  })

  const storageStats = computed(() => {
    return systemStatus.value?.storage || {}
  })

  const monitorStats = computed(() => {
    return systemStatus.value?.monitor || {}
  })

  const websocketStats = computed(() => {
    return systemStatus.value?.websocket || {}
  })

  // 方法
  const fetchSystemInfo = async () => {
    try {
      loading.value = true
      systemInfo.value = await systemApi.getInfo()
    } catch (error) {
      console.error('获取系统信息失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchSystemStatus = async () => {
    try {
      systemStatus.value = await systemApi.getStatus()
      lastUpdateTime.value = new Date()
    } catch (error) {
      console.error('获取系统状态失败:', error)
    }
  }

  const fetchSystemHealth = async () => {
    try {
      systemHealth.value = await systemApi.getHealth()
    } catch (error) {
      console.error('获取系统健康状态失败:', error)
    }
  }

  const fetchSystemStats = async () => {
    try {
      systemStats.value = await systemApi.getStats()
    } catch (error) {
      console.error('获取系统统计失败:', error)
    }
  }

  const fetchSystemConfig = async () => {
    try {
      systemConfig.value = await systemApi.getConfig()
    } catch (error) {
      console.error('获取系统配置失败:', error)
    }
  }

  const triggerGC = async () => {
    try {
      const result = await systemApi.triggerGC()
      // 触发GC后刷新系统状态
      await fetchSystemStatus()
      return result
    } catch (error) {
      console.error('触发垃圾回收失败:', error)
      throw error
    }
  }

  const fetchAllData = async () => {
    await Promise.all([
      fetchSystemInfo(),
      fetchSystemStatus(),
      fetchSystemHealth(),
      fetchSystemStats(),
      fetchSystemConfig()
    ])
  }

  const startAutoRefresh = (interval: number = 30000) => {
    // 每30秒自动刷新系统状态
    const timer = setInterval(() => {
      fetchSystemStatus()
      fetchSystemHealth()
    }, interval)

    // 返回清理函数
    return () => clearInterval(timer)
  }

  return {
    // 状态
    systemInfo,
    systemStatus,
    systemHealth,
    systemStats,
    systemConfig,
    loading,
    lastUpdateTime,

    // 计算属性
    isHealthy,
    memoryUsage,
    memoryUsageColor,
    storageStats,
    monitorStats,
    websocketStats,

    // 方法
    fetchSystemInfo,
    fetchSystemStatus,
    fetchSystemHealth,
    fetchSystemStats,
    fetchSystemConfig,
    triggerGC,
    fetchAllData,
    startAutoRefresh
  }
})
