import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { foldersApi } from '@/api/folders'
import type { FolderInfo, VideoInfo, PageResponse } from '@/types'

export const useFoldersStore = defineStore('folders', () => {
  // 状态
  const folders = ref<FolderInfo[]>([])
  const currentFolder = ref<FolderInfo | null>(null)
  const folderVideos = ref<PageResponse<VideoInfo> | null>(null)
  const folderStats = ref<any>(null)
  const loading = ref(false)
  const videosLoading = ref(false)

  // 计算属性
  const totalFolders = computed(() => folders.value.length)

  const activeFolders = computed(() => 
    folders.value.filter(folder => 
      folder.monitorEnabled || folder.preloadEnabled
    )
  )

  const monitoringFolders = computed(() => 
    folders.value.filter(folder => 
      folder.monitorEnabled && folder.monitorStatus === 'RUNNING'
    )
  )

  const preloadingFolders = computed(() => 
    folders.value.filter(folder => 
      folder.preloadEnabled && folder.preloadStatus === 'RUNNING'
    )
  )

  const totalVideos = computed(() => 
    folders.value.reduce((sum, folder) => sum + (folder.videoCount || 0), 0)
  )

  const validVideos = computed(() => 
    folders.value.reduce((sum, folder) => sum + (folder.validVideoCount || 0), 0)
  )

  const invalidVideos = computed(() => 
    folders.value.reduce((sum, folder) => sum + (folder.invalidVideoCount || 0), 0)
  )

  const validRate = computed(() => {
    const total = totalVideos.value
    return total > 0 ? (validVideos.value / total * 100) : 0
  })

  // 方法
  const fetchFolders = async () => {
    try {
      loading.value = true
      folders.value = await foldersApi.getAll()
    } catch (error) {
      console.error('获取收藏夹列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchFolderById = async (folderId: number) => {
    try {
      const folder = await foldersApi.getById(folderId)
      currentFolder.value = folder
      
      // 更新列表中的收藏夹信息
      const index = folders.value.findIndex(f => f.folderId === folderId)
      if (index !== -1) {
        folders.value[index] = folder
      }
      
      return folder
    } catch (error) {
      console.error('获取收藏夹详情失败:', error)
      throw error
    }
  }

  const addFolder = async (folderId: number) => {
    try {
      const folder = await foldersApi.add(folderId)
      folders.value.push(folder)
      return folder
    } catch (error) {
      console.error('添加收藏夹失败:', error)
      throw error
    }
  }

  const updateFolder = async (folderId: number, data: Partial<FolderInfo>) => {
    try {
      const updatedFolder = await foldersApi.update(folderId, data)
      
      // 更新列表中的收藏夹
      const index = folders.value.findIndex(f => f.folderId === folderId)
      if (index !== -1) {
        folders.value[index] = updatedFolder
      }
      
      // 更新当前收藏夹
      if (currentFolder.value?.folderId === folderId) {
        currentFolder.value = updatedFolder
      }
      
      return updatedFolder
    } catch (error) {
      console.error('更新收藏夹失败:', error)
      throw error
    }
  }

  const deleteFolder = async (folderId: number) => {
    try {
      await foldersApi.delete(folderId)
      
      // 从列表中移除
      const index = folders.value.findIndex(f => f.folderId === folderId)
      if (index !== -1) {
        folders.value.splice(index, 1)
      }
      
      // 清空当前收藏夹
      if (currentFolder.value?.folderId === folderId) {
        currentFolder.value = null
      }
    } catch (error) {
      console.error('删除收藏夹失败:', error)
      throw error
    }
  }

  const refreshFolder = async (folderId: number) => {
    try {
      const folder = await foldersApi.refresh(folderId)
      
      // 更新列表中的收藏夹
      const index = folders.value.findIndex(f => f.folderId === folderId)
      if (index !== -1) {
        folders.value[index] = folder
      }
      
      // 更新当前收藏夹
      if (currentFolder.value?.folderId === folderId) {
        currentFolder.value = folder
      }
      
      return folder
    } catch (error) {
      console.error('刷新收藏夹失败:', error)
      throw error
    }
  }

  const fetchFolderVideos = async (
    folderId: number, 
    params: { page?: number; size?: number; status?: 'valid' | 'invalid' } = {}
  ) => {
    try {
      videosLoading.value = true
      folderVideos.value = await foldersApi.getVideos(folderId, params)
      return folderVideos.value
    } catch (error) {
      console.error('获取收藏夹视频失败:', error)
      throw error
    } finally {
      videosLoading.value = false
    }
  }

  const fetchFolderStats = async (folderId: number) => {
    try {
      folderStats.value = await foldersApi.getStats(folderId)
      return folderStats.value
    } catch (error) {
      console.error('获取收藏夹统计失败:', error)
      throw error
    }
  }

  const getFolderById = (folderId: number) => {
    return folders.value.find(f => f.folderId === folderId)
  }

  const updateFolderStatus = (folderId: number, updates: Partial<FolderInfo>) => {
    const folder = getFolderById(folderId)
    if (folder) {
      Object.assign(folder, updates)
      
      // 如果是当前收藏夹，也更新
      if (currentFolder.value?.folderId === folderId) {
        Object.assign(currentFolder.value, updates)
      }
    }
  }

  const clearCurrentFolder = () => {
    currentFolder.value = null
    folderVideos.value = null
    folderStats.value = null
  }

  return {
    // 状态
    folders,
    currentFolder,
    folderVideos,
    folderStats,
    loading,
    videosLoading,

    // 计算属性
    totalFolders,
    activeFolders,
    monitoringFolders,
    preloadingFolders,
    totalVideos,
    validVideos,
    invalidVideos,
    validRate,

    // 方法
    fetchFolders,
    fetchFolderById,
    addFolder,
    updateFolder,
    deleteFolder,
    refreshFolder,
    fetchFolderVideos,
    fetchFolderStats,
    getFolderById,
    updateFolderStatus,
    clearCurrentFolder
  }
})
