import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { monitorApi } from '@/api/monitor'
import { useFoldersStore } from './folders'

export const useMonitorStore = defineStore('monitor', () => {
  // 状态
  const monitorStatus = ref<any>(null)
  const monitorLogs = ref<any[]>([])
  const loading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)

  // 获取folders store
  const foldersStore = useFoldersStore()

  // 计算属性
  const isMonitorEnabled = computed(() => {
    return monitorStatus.value?.enabled === true
  })

  const monitoringCount = computed(() => {
    return monitorStatus.value?.monitoringCount || 0
  })

  const maxConcurrent = computed(() => {
    return monitorStatus.value?.maxConcurrent || 0
  })

  const monitoringFolders = computed(() => {
    return monitorStatus.value?.monitoringFolders || []
  })

  const canStartMore = computed(() => {
    return monitoringCount.value < maxConcurrent.value
  })

  const monitoringRate = computed(() => {
    const total = foldersStore.totalFolders
    return total > 0 ? (monitoringCount.value / total * 100) : 0
  })

  // 方法
  const fetchMonitorStatus = async () => {
    try {
      monitorStatus.value = await monitorApi.getStatus()
      lastUpdateTime.value = new Date()
    } catch (error) {
      console.error('获取监控状态失败:', error)
    }
  }

  const startMonitor = async (folderId: number) => {
    try {
      loading.value = true
      await monitorApi.start(folderId)
      
      // 更新收藏夹状态
      foldersStore.updateFolderStatus(folderId, {
        monitorEnabled: true,
        monitorStatus: 'RUNNING'
      })
      
      // 刷新监控状态
      await fetchMonitorStatus()
    } catch (error) {
      console.error('启动监控失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const stopMonitor = async (folderId: number) => {
    try {
      loading.value = true
      await monitorApi.stop(folderId)
      
      // 更新收藏夹状态
      foldersStore.updateFolderStatus(folderId, {
        monitorEnabled: false,
        monitorStatus: 'STOPPED'
      })
      
      // 刷新监控状态
      await fetchMonitorStatus()
    } catch (error) {
      console.error('停止监控失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const executeMonitor = async (folderId: number) => {
    try {
      await monitorApi.execute(folderId)
    } catch (error) {
      console.error('执行监控任务失败:', error)
      throw error
    }
  }

  const startAllMonitors = async () => {
    try {
      loading.value = true
      const result = await monitorApi.startAll()
      
      // 刷新监控状态
      await fetchMonitorStatus()
      
      return result
    } catch (error) {
      console.error('批量启动监控失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const stopAllMonitors = async () => {
    try {
      loading.value = true
      const result = await monitorApi.stopAll()
      
      // 刷新监控状态
      await fetchMonitorStatus()
      
      return result
    } catch (error) {
      console.error('批量停止监控失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateMonitorConfig = async (folderId: number, config: {
    monitorEnabled?: boolean
    monitorInterval?: number
  }) => {
    try {
      const updatedFolder = await monitorApi.updateConfig(folderId, config)
      
      // 更新收藏夹信息
      foldersStore.updateFolderStatus(folderId, updatedFolder)
      
      return updatedFolder
    } catch (error) {
      console.error('更新监控配置失败:', error)
      throw error
    }
  }

  const fetchMonitorLogs = async (limit: number = 50) => {
    try {
      monitorLogs.value = await monitorApi.getLogs(limit)
    } catch (error) {
      console.error('获取监控日志失败:', error)
    }
  }

  const isMonitoring = (folderId: number) => {
    return monitoringFolders.value.includes(folderId)
  }

  const toggleMonitor = async (folderId: number) => {
    if (isMonitoring(folderId)) {
      await stopMonitor(folderId)
    } else {
      await startMonitor(folderId)
    }
  }

  const handleMonitorStatusChange = (folderId: number, status: string) => {
    // 处理WebSocket监控状态变化事件
    const monitorStatus = status === 'STARTED' ? 'RUNNING' : 'STOPPED'
    const monitorEnabled = status === 'STARTED'
    
    foldersStore.updateFolderStatus(folderId, {
      monitorEnabled,
      monitorStatus: monitorStatus as any
    })
    
    // 刷新监控状态
    fetchMonitorStatus()
  }

  const startAutoRefresh = (interval: number = 10000) => {
    // 每10秒自动刷新监控状态
    const timer = setInterval(() => {
      fetchMonitorStatus()
    }, interval)

    // 返回清理函数
    return () => clearInterval(timer)
  }

  return {
    // 状态
    monitorStatus,
    monitorLogs,
    loading,
    lastUpdateTime,

    // 计算属性
    isMonitorEnabled,
    monitoringCount,
    maxConcurrent,
    monitoringFolders,
    canStartMore,
    monitoringRate,

    // 方法
    fetchMonitorStatus,
    startMonitor,
    stopMonitor,
    executeMonitor,
    startAllMonitors,
    stopAllMonitors,
    updateMonitorConfig,
    fetchMonitorLogs,
    isMonitoring,
    toggleMonitor,
    handleMonitorStatusChange,
    startAutoRefresh
  }
})
