import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { preloadApi } from '@/api/preload'
import { useFoldersStore } from './folders'
import type { PreloadInfo, PageResponse } from '@/types'

export const usePreloadStore = defineStore('preload', () => {
  // 状态
  const preloadStatus = ref<any>(null)
  const folderPreloads = ref<PageResponse<PreloadInfo> | null>(null)
  const preloadStats = ref<any>(null)
  const preloadProgress = ref<Map<number, any>>(new Map())
  const loading = ref(false)
  const preloadsLoading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)

  // 获取folders store
  const foldersStore = useFoldersStore()

  // 计算属性
  const isPreloadEnabled = computed(() => {
    return preloadStatus.value?.enabled === true
  })

  const preloadingCount = computed(() => {
    return preloadStatus.value?.preloadingCount || 0
  })

  const maxConcurrent = computed(() => {
    return preloadStatus.value?.maxConcurrent || 0
  })

  const defaultStrategy = computed(() => {
    return preloadStatus.value?.defaultStrategy || 'smart'
  })

  const preloadingFolders = computed(() => {
    return preloadStatus.value?.preloadingFolders || []
  })

  const availableStrategies = computed(() => {
    return preloadStatus.value?.availableStrategies || ['smart', 'full']
  })

  const canStartMore = computed(() => {
    return preloadingCount.value < maxConcurrent.value
  })

  const preloadingRate = computed(() => {
    const total = foldersStore.totalFolders
    return total > 0 ? (preloadingCount.value / total * 100) : 0
  })

  // 方法
  const fetchPreloadStatus = async () => {
    try {
      preloadStatus.value = await preloadApi.getStatus()
      lastUpdateTime.value = new Date()
    } catch (error) {
      console.error('获取预下载状态失败:', error)
    }
  }

  const startPreload = async (folderId: number) => {
    try {
      loading.value = true
      await preloadApi.start(folderId)
      
      // 更新收藏夹状态
      foldersStore.updateFolderStatus(folderId, {
        preloadEnabled: true,
        preloadStatus: 'RUNNING'
      })
      
      // 刷新预下载状态
      await fetchPreloadStatus()
    } catch (error) {
      console.error('启动预下载失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const stopPreload = async (folderId: number) => {
    try {
      loading.value = true
      await preloadApi.stop(folderId)
      
      // 更新收藏夹状态
      foldersStore.updateFolderStatus(folderId, {
        preloadEnabled: false,
        preloadStatus: 'STOPPED'
      })
      
      // 清除进度信息
      preloadProgress.value.delete(folderId)
      
      // 刷新预下载状态
      await fetchPreloadStatus()
    } catch (error) {
      console.error('停止预下载失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const executePreload = async (folderId: number) => {
    try {
      await preloadApi.execute(folderId)
    } catch (error) {
      console.error('执行预下载任务失败:', error)
      throw error
    }
  }

  const fetchFolderPreloads = async (
    folderId: number,
    params: { page?: number; size?: number; status?: string } = {}
  ) => {
    try {
      preloadsLoading.value = true
      folderPreloads.value = await preloadApi.getFolderPreloads(folderId, params)
      return folderPreloads.value
    } catch (error) {
      console.error('获取收藏夹预下载信息失败:', error)
      throw error
    } finally {
      preloadsLoading.value = false
    }
  }

  const fetchPreloadStats = async (folderId: number) => {
    try {
      preloadStats.value = await preloadApi.getStats(folderId)
      return preloadStats.value
    } catch (error) {
      console.error('获取预下载统计失败:', error)
      throw error
    }
  }

  const refreshExpiredLinks = async () => {
    try {
      await preloadApi.refreshExpired()
    } catch (error) {
      console.error('刷新过期链接失败:', error)
      throw error
    }
  }

  const updatePreloadConfig = async (folderId: number, config: {
    preloadEnabled?: boolean
    preloadStrategy?: string
    preloadInterval?: number
  }) => {
    try {
      const updatedFolder = await preloadApi.updateConfig(folderId, config)
      
      // 更新收藏夹信息
      foldersStore.updateFolderStatus(folderId, updatedFolder)
      
      return updatedFolder
    } catch (error) {
      console.error('更新预下载配置失败:', error)
      throw error
    }
  }

  const startAllPreloads = async () => {
    try {
      loading.value = true
      const result = await preloadApi.startAll()
      
      // 刷新预下载状态
      await fetchPreloadStatus()
      
      return result
    } catch (error) {
      console.error('批量启动预下载失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const stopAllPreloads = async () => {
    try {
      loading.value = true
      const result = await preloadApi.stopAll()
      
      // 清除所有进度信息
      preloadProgress.value.clear()
      
      // 刷新预下载状态
      await fetchPreloadStatus()
      
      return result
    } catch (error) {
      console.error('批量停止预下载失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const isPreloading = (folderId: number) => {
    return preloadingFolders.value.includes(folderId)
  }

  const togglePreload = async (folderId: number) => {
    if (isPreloading(folderId)) {
      await stopPreload(folderId)
    } else {
      await startPreload(folderId)
    }
  }

  const getPreloadProgress = (folderId: number) => {
    return preloadProgress.value.get(folderId) || { completed: 0, total: 0, progress: 0 }
  }

  const updatePreloadProgress = (folderId: number, progress: any) => {
    preloadProgress.value.set(folderId, progress)
  }

  const handlePreloadStatusChange = (folderId: number, status: string) => {
    // 处理WebSocket预下载状态变化事件
    const preloadStatus = status === 'STARTED' ? 'RUNNING' : 'STOPPED'
    const preloadEnabled = status === 'STARTED'
    
    foldersStore.updateFolderStatus(folderId, {
      preloadEnabled,
      preloadStatus: preloadStatus as any
    })
    
    // 如果停止了，清除进度信息
    if (status === 'STOPPED') {
      preloadProgress.value.delete(folderId)
    }
    
    // 刷新预下载状态
    fetchPreloadStatus()
  }

  const handlePreloadProgress = (data: any) => {
    const { folderId, completed, total, progress } = data
    updatePreloadProgress(folderId, { completed, total, progress })
  }

  const getStrategyDisplayName = (strategy: string) => {
    const strategyNames: Record<string, string> = {
      'smart': '智能策略',
      'full': '全量策略'
    }
    return strategyNames[strategy] || strategy
  }

  const startAutoRefresh = (interval: number = 15000) => {
    // 每15秒自动刷新预下载状态
    const timer = setInterval(() => {
      fetchPreloadStatus()
    }, interval)

    // 返回清理函数
    return () => clearInterval(timer)
  }

  return {
    // 状态
    preloadStatus,
    folderPreloads,
    preloadStats,
    preloadProgress,
    loading,
    preloadsLoading,
    lastUpdateTime,

    // 计算属性
    isPreloadEnabled,
    preloadingCount,
    maxConcurrent,
    defaultStrategy,
    preloadingFolders,
    availableStrategies,
    canStartMore,
    preloadingRate,

    // 方法
    fetchPreloadStatus,
    startPreload,
    stopPreload,
    executePreload,
    fetchFolderPreloads,
    fetchPreloadStats,
    refreshExpiredLinks,
    updatePreloadConfig,
    startAllPreloads,
    stopAllPreloads,
    isPreloading,
    togglePreload,
    getPreloadProgress,
    updatePreloadProgress,
    handlePreloadStatusChange,
    handlePreloadProgress,
    getStrategyDisplayName,
    startAutoRefresh
  }
})
