<template>
  <div class="app-sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo" @click="goHome">
      <div class="logo-icon">
        <el-icon size="28">
          <Monitor />
        </el-icon>
      </div>
      <transition name="fade">
        <div v-show="!collapse" class="logo-text">
          <div class="logo-title">MeBiliDown</div>
          <div class="logo-subtitle">监控与预下载</div>
        </div>
      </transition>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="collapse"
      :unique-opened="true"
      class="sidebar-menu"
      background-color="var(--el-bg-color)"
      text-color="var(--el-text-color-primary)"
      active-text-color="var(--el-color-primary)"
      @select="handleMenuSelect"
    >
      <el-menu-item
        v-for="item in menuItems"
        :key="item.path"
        :index="item.path"
        class="menu-item"
      >
        <el-icon>
          <component :is="item.icon" />
        </el-icon>
        <template #title>
          <span>{{ item.title }}</span>
          <el-badge
            v-if="item.badge && item.badge.value > 0"
            :value="item.badge.value"
            :type="item.badge.type"
            class="menu-badge"
          />
        </template>
      </el-menu-item>
    </el-menu>

    <!-- 底部工具栏 -->
    <div class="sidebar-footer">
      <!-- 折叠按钮 -->
      <el-tooltip
        :content="collapse ? '展开侧边栏' : '折叠侧边栏'"
        placement="right"
      >
        <el-button
          :icon="collapse ? Expand : Fold"
          circle
          size="small"
          @click="toggleCollapse"
        />
      </el-tooltip>

      <!-- 连接状态 -->
      <transition name="fade">
        <div v-show="!collapse" class="connection-status">
          <el-tooltip :content="connectionTooltip" placement="top">
            <div class="status-indicator" :class="connectionStatusClass">
              <el-icon size="12">
                <component :is="connectionIcon" />
              </el-icon>
              <span class="status-text">{{ connectionText }}</span>
            </div>
          </el-tooltip>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Monitor,
  Folder,
  View,
  Download,
  Setting,
  Tools,
  Expand,
  Fold,
  Connection,
  Close
} from '@element-plus/icons-vue'
import { useWebSocketStore } from '@/stores/websocket'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'

// Props
interface Props {
  collapse: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  toggle: []
}>()

// 路由
const route = useRoute()
const router = useRouter()

// 状态管理
const wsStore = useWebSocketStore()
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 菜单项配置
const menuItems = computed(() => [
  {
    path: '/dashboard',
    title: '仪表板',
    icon: Monitor,
    badge: null
  },
  {
    path: '/folders',
    title: '收藏夹管理',
    icon: Folder,
    badge: {
      value: foldersStore.totalFolders,
      type: 'primary' as const
    }
  },
  {
    path: '/monitor',
    title: '监控管理',
    icon: View,
    badge: {
      value: monitorStore.monitoringCount,
      type: monitorStore.monitoringCount > 0 ? 'success' as const : 'info' as const
    }
  },
  {
    path: '/preload',
    title: '预下载管理',
    icon: Download,
    badge: {
      value: preloadStore.preloadingCount,
      type: preloadStore.preloadingCount > 0 ? 'success' as const : 'info' as const
    }
  },
  {
    path: '/system',
    title: '系统状态',
    icon: Setting,
    badge: null
  },
  {
    path: '/settings',
    title: '系统设置',
    icon: Tools,
    badge: null
  }
])

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  // 处理子路由，如 /folders/123 -> /folders
  const menuPath = menuItems.value.find(item => path.startsWith(item.path))?.path
  return menuPath || path
})

// 连接状态
const connectionStatusClass = computed(() => ({
  'status-connected': wsStore.isConnected,
  'status-disconnected': !wsStore.isConnected
}))

const connectionIcon = computed(() => 
  wsStore.isConnected ? Connection : Close
)

const connectionText = computed(() => 
  wsStore.isConnected ? '已连接' : '未连接'
)

const connectionTooltip = computed(() => {
  if (wsStore.isConnected) {
    return `WebSocket已连接\n状态: ${wsStore.connectionStatus}`
  } else {
    return `WebSocket未连接\n状态: ${wsStore.connectionStatus}`
  }
})

// 方法
const handleMenuSelect = (path: string) => {
  if (route.path !== path) {
    router.push(path)
  }
}

const toggleCollapse = () => {
  emit('toggle')
}

const goHome = () => {
  if (route.path !== '/dashboard') {
    router.push('/dashboard')
  }
}

// 监听路由变化，确保菜单状态正确
watch(
  () => route.path,
  () => {
    // 路由变化时的处理逻辑
  },
  { immediate: true }
)
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

/* Logo区域 */
.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-logo:hover {
  background: var(--el-fill-color-light);
}

.logo-icon {
  color: var(--el-color-primary);
  margin-right: 12px;
  flex-shrink: 0;
}

.logo-text {
  overflow: hidden;
}

.logo-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
  margin-top: 2px;
}

/* 菜单样式 */
.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.menu-item {
  position: relative;
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: var(--el-fill-color-light) !important;
}

.menu-item.is-active {
  background: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
}

.menu-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 底部工具栏 */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  background: var(--el-fill-color-lighter);
  transition: all 0.3s ease;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-connected {
  color: var(--el-color-success);
}

.status-disconnected {
  color: var(--el-color-danger);
}

.status-text {
  font-weight: 500;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式 */
@media (max-width: 768px) {
  .sidebar-logo {
    padding: 0 16px;
  }
  
  .sidebar-footer {
    padding: 12px;
  }
}

/* 暗色主题适配 */
.dark .sidebar-logo:hover {
  background: var(--el-fill-color-dark);
}

.dark .menu-item:hover {
  background: var(--el-fill-color-dark) !important;
}

.dark .connection-status {
  background: var(--el-fill-color-dark);
}
</style>
