<template>
  <div class="app-header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 折叠按钮 -->
      <el-button
        :icon="collapse ? Expand : Fold"
        circle
        size="large"
        @click="toggleSidebar"
      />
      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item
          v-for="item in breadcrumbItems"
          :key="item.path"
          :to="item.path"
        >
          <el-icon v-if="item.icon" class="breadcrumb-icon">
            <component :is="item.icon" />
          </el-icon>
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 系统状态指示器 -->
      <div class="status-indicators">
        <!-- 内存使用率 -->
        <el-tooltip content="内存使用率" placement="bottom">
          <div class="status-item">
            <el-icon>
              <Cpu />
            </el-icon>
            <el-progress
              :percentage="memoryUsage"
              :color="memoryColor"
              :show-text="false"
              :stroke-width="4"
              class="mini-progress"
            />
            <span class="status-text">{{ memoryUsage.toFixed(1) }}%</span>
          </div>
        </el-tooltip>

        <!-- 监控状态 */
        <el-tooltip content="监控任务" placement="bottom">
          <div class="status-item">
            <el-icon :color="monitorColor">
              <View />
            </el-icon>
            <span class="status-text">{{ monitoringCount }}</span>
          </div>
        </el-tooltip>

        <!-- 预下载状态 -->
        <el-tooltip content="预下载任务" placement="bottom">
          <div class="status-item">
            <el-icon :color="preloadColor">
              <Download />
            </el-icon>
            <span class="status-text">{{ preloadingCount }}</span>
          </div>
        </el-tooltip>

        <!-- WebSocket连接状态 -->
        <el-tooltip :content="wsTooltip" placement="bottom">
          <div class="status-item">
            <div class="connection-dot" :class="wsStatusClass"></div>
            <span class="status-text">实时</span>
          </div>
        </el-tooltip>
      </div>

      <!-- 操作按钮 -->
      <div class="header-actions">
        <!-- 刷新按钮 -->
        <el-tooltip content="刷新数据 (Ctrl+R)" placement="bottom">
          <el-button
            :icon="Refresh"
            :loading="isRefreshing"
            circle
            @click="handleRefresh"
          />
        </el-tooltip>

        <!-- 全屏按钮 -->
        <el-tooltip :content="isFullscreen ? '退出全屏' : '进入全屏'" placement="bottom">
          <el-button
            :icon="isFullscreen ? OffScreen : FullScreen"
            circle
            @click="toggleFullscreen"
          />
        </el-tooltip>

        <!-- 设置下拉菜单 -->
        <el-dropdown @command="handleCommand">
          <el-button :icon="Setting" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">
                <el-icon><Tools /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item command="gc">
                <el-icon><Delete /></el-icon>
                垃圾回收
              </el-dropdown-item>
              <el-dropdown-item divided command="about">
                <el-icon><InfoFilled /></el-icon>
                关于系统
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Expand,
  Fold,
  Refresh,
  FullScreen,
  OffScreen,
  Setting,
  Tools,
  Delete,
  InfoFilled,
  Cpu,
  View,
  Download,
  Monitor,
  Folder,
  Document
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '@/stores/system'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { useWebSocketStore } from '@/stores/websocket'

// Props
interface Props {
  collapse: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  toggle: []
  refresh: []
}>()

// 路由
const route = useRoute()
const router = useRouter()

// 状态管理
const systemStore = useSystemStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()
const wsStore = useWebSocketStore()

// 响应式状态
const isRefreshing = ref(false)
const isFullscreen = ref(false)

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = []
  
  // 根据路由生成面包屑
  switch (route.name) {
    case 'Dashboard':
      items.push({ title: '仪表板', path: '/dashboard', icon: Monitor })
      break
    case 'Folders':
      items.push({ title: '收藏夹管理', path: '/folders', icon: Folder })
      break
    case 'FolderDetail':
      items.push({ title: '收藏夹管理', path: '/folders', icon: Folder })
      items.push({ title: '收藏夹详情', path: route.path, icon: Document })
      break
    case 'Monitor':
      items.push({ title: '监控管理', path: '/monitor', icon: View })
      break
    case 'Preload':
      items.push({ title: '预下载管理', path: '/preload', icon: Download })
      break
    case 'System':
      items.push({ title: '系统状态', path: '/system', icon: Setting })
      break
    case 'Settings':
      items.push({ title: '系统设置', path: '/settings', icon: Tools })
      break
    default:
      items.push({ title: '未知页面', path: route.path })
  }
  
  return items
})

// 系统状态
const memoryUsage = computed(() => systemStore.memoryUsage || 0)
const memoryColor = computed(() => systemStore.memoryUsageColor)

const monitoringCount = computed(() => monitorStore.monitoringCount)
const monitorColor = computed(() => 
  monitoringCount.value > 0 ? '#67c23a' : '#909399'
)

const preloadingCount = computed(() => preloadStore.preloadingCount)
const preloadColor = computed(() => 
  preloadingCount.value > 0 ? '#67c23a' : '#909399'
)

// WebSocket状态
const wsStatusClass = computed(() => ({
  'connected': wsStore.isConnected,
  'disconnected': !wsStore.isConnected
}))

const wsTooltip = computed(() => 
  `WebSocket: ${wsStore.connectionStatus}`
)

// 方法
const toggleSidebar = () => {
  emit('toggle')
}

const handleRefresh = async () => {
  try {
    isRefreshing.value = true
    emit('refresh')
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    // 延迟重置加载状态，确保用户能看到反馈
    setTimeout(() => {
      isRefreshing.value = false
    }, 1000)
  }
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'settings':
      router.push('/settings')
      break
      
    case 'gc':
      try {
        await ElMessageBox.confirm(
          '确定要执行垃圾回收吗？这可能会暂时影响系统性能。',
          '垃圾回收',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await systemStore.triggerGC()
        ElMessage.success('垃圾回收执行成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('垃圾回收执行失败')
        }
      }
      break
      
    case 'about':
      ElMessageBox.alert(
        `
        <div style="text-align: center;">
          <h3>MeBiliDown</h3>
          <p>现代化B站收藏夹监控与预下载系统</p>
          <p>版本: 1.0.0</p>
          <p>技术栈: Vue 3 + TypeScript + Element Plus</p>
          <p>后端: Spring Boot + Java</p>
        </div>
        `,
        '关于系统',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
      break
  }
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', () => {
  isFullscreen.value = !!document.fullscreenElement
})
</script>

<style scoped>
.app-header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb-icon {
  margin-right: 4px;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.mini-progress {
  width: 40px;
}

.status-text {
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.connection-dot.connected {
  background: #67c23a;
  box-shadow: 0 0 6px rgba(103, 194, 58, 0.6);
  animation: pulse 2s infinite;
}

.connection-dot.disconnected {
  background: #f56c6c;
}

/* 操作按钮 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .status-indicators {
    display: none;
  }
  
  .breadcrumb {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-actions {
    gap: 4px;
  }
  
  .header-actions .el-button {
    padding: 8px;
  }
}
</style>
