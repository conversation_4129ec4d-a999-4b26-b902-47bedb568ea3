<template>
  <el-container class="app-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="app-aside">
      <AppSidebar :collapse="isCollapse" @toggle="toggleSidebar" />
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="app-main">
      <!-- 顶部导航 -->
      <el-header height="60px" class="app-header">
        <AppHeader 
          :collapse="isCollapse" 
          @toggle="toggleSidebar"
          @refresh="handleRefresh"
        />
      </el-header>

      <!-- 内容区域 -->
      <el-main class="app-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <keep-alive :include="keepAliveComponents">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>

      <!-- 底部状态栏 -->
      <el-footer height="40px" class="app-footer">
        <AppFooter />
      </el-footer>
    </el-container>

    <!-- 全局加载遮罩 -->
    <el-loading 
      v-loading="globalLoading"
      :text="loadingText"
      background="rgba(0, 0, 0, 0.7)"
      element-loading-svg-view-box="-10, -10, 50, 50"
    />
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import { useSystemStore } from '@/stores/system'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'

// 响应式状态
const isCollapse = ref(false)
const globalLoading = ref(false)
const loadingText = ref('加载中...')

// 路由
const route = useRoute()

// 状态管理
const systemStore = useSystemStore()
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 需要缓存的组件
const keepAliveComponents = ['Dashboard', 'Folders', 'Monitor', 'Preload']

// 计算属性
const currentLoading = computed(() => {
  return systemStore.loading || 
         foldersStore.loading || 
         monitorStore.loading || 
         preloadStore.loading
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
  // 保存到本地存储
  localStorage.setItem('sidebar-collapse', isCollapse.value.toString())
}

const handleRefresh = async () => {
  try {
    globalLoading.value = true
    loadingText.value = '刷新数据中...'
    
    // 根据当前路由刷新对应数据
    switch (route.name) {
      case 'Dashboard':
        await Promise.all([
          systemStore.fetchAllData(),
          foldersStore.fetchFolders(),
          monitorStore.fetchMonitorStatus(),
          preloadStore.fetchPreloadStatus()
        ])
        break
      case 'Folders':
        await foldersStore.fetchFolders()
        break
      case 'Monitor':
        await Promise.all([
          monitorStore.fetchMonitorStatus(),
          foldersStore.fetchFolders()
        ])
        break
      case 'Preload':
        await Promise.all([
          preloadStore.fetchPreloadStatus(),
          foldersStore.fetchFolders()
        ])
        break
      case 'System':
        await systemStore.fetchAllData()
        break
      default:
        await foldersStore.fetchFolders()
    }
    
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    globalLoading.value = false
  }
}

// 响应式布局
const handleResize = () => {
  const width = window.innerWidth
  if (width < 768) {
    isCollapse.value = true
  }
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + R: 刷新
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault()
    handleRefresh()
  }
  
  // Ctrl/Cmd + B: 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    toggleSidebar()
  }
}

onMounted(() => {
  // 恢复侧边栏状态
  const savedCollapse = localStorage.getItem('sidebar-collapse')
  if (savedCollapse !== null) {
    isCollapse.value = savedCollapse === 'true'
  }
  
  // 响应式处理
  window.addEventListener('resize', handleResize)
  handleResize()
  
  // 键盘快捷键
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  background: var(--el-bg-color-page);
}

.app-aside {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0;
  display: flex;
  align-items: center;
}

.app-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: var(--el-bg-color-page);
}

.app-footer {
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  padding: 0;
  display: flex;
  align-items: center;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式 */
@media (max-width: 768px) {
  .app-content {
    padding: 10px;
  }
}
</style>
