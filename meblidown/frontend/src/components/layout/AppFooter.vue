<template>
  <div class="app-footer">
    <!-- 左侧信息 -->
    <div class="footer-left">
      <span class="footer-text">
        MeBiliDown v1.0.0
      </span>
      <el-divider direction="vertical" />
      <span class="footer-text">
        运行时间: {{ uptime }}
      </span>
      <el-divider direction="vertical" />
      <span class="footer-text">
        最后更新: {{ lastUpdateTime }}
      </span>
    </div>

    <!-- 中间状态 -->
    <div class="footer-center">
      <!-- 收藏夹统计 -->
      <div class="stat-item">
        <el-icon><Folder /></el-icon>
        <span>{{ foldersStore.totalFolders }} 个收藏夹</span>
      </div>
      
      <!-- 视频统计 -->
      <div class="stat-item">
        <el-icon><VideoPlay /></el-icon>
        <span>{{ foldersStore.totalVideos }} 个视频</span>
      </div>
      
      <!-- 有效率 -->
      <div class="stat-item">
        <el-icon><SuccessFilled /></el-icon>
        <span>{{ validRate }}% 有效</span>
      </div>
    </div>

    <!-- 右侧状态 -->
    <div class="footer-right">
      <!-- 系统健康状态 -->
      <el-tooltip :content="healthTooltip" placement="top">
        <div class="health-indicator" :class="healthClass">
          <el-icon>
            <component :is="healthIcon" />
          </el-icon>
          <span>{{ healthText }}</span>
        </div>
      </el-tooltip>

      <!-- WebSocket状态 */
      <el-tooltip :content="wsTooltip" placement="top">
        <div class="ws-indicator" :class="wsClass">
          <div class="ws-dot"></div>
          <span>{{ wsText }}</span>
        </div>
      </el-tooltip>

      <!-- 内存使用 -->
      <el-tooltip :content="memoryTooltip" placement="top">
        <div class="memory-indicator">
          <el-icon><Cpu /></el-icon>
          <el-progress
            :percentage="memoryUsage"
            :color="memoryColor"
            :show-text="false"
            :stroke-width="3"
            class="memory-progress"
          />
          <span class="memory-text">{{ memoryUsage.toFixed(1) }}%</span>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import {
  Folder,
  VideoPlay,
  SuccessFilled,
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  Cpu
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import { useSystemStore } from '@/stores/system'
import { useFoldersStore } from '@/stores/folders'
import { useWebSocketStore } from '@/stores/websocket'

// 状态管理
const systemStore = useSystemStore()
const foldersStore = useFoldersStore()
const wsStore = useWebSocketStore()

// 响应式状态
const currentTime = ref(new Date())
const startTime = ref(new Date())

// 计算属性
const uptime = computed(() => {
  const diff = currentTime.value.getTime() - startTime.value.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`
  } else {
    return `${seconds}s`
  }
})

const lastUpdateTime = computed(() => {
  const time = systemStore.lastUpdateTime
  return time ? formatTime.relative(time) : '未知'
})

const validRate = computed(() => {
  return foldersStore.validRate.toFixed(1)
})

// 系统健康状态
const healthClass = computed(() => ({
  'health-good': systemStore.isHealthy,
  'health-warning': !systemStore.isHealthy && systemStore.systemStatus,
  'health-error': !systemStore.isHealthy && !systemStore.systemStatus
}))

const healthIcon = computed(() => {
  if (systemStore.isHealthy) return CircleCheckFilled
  if (systemStore.systemStatus) return WarningFilled
  return CircleCloseFilled
})

const healthText = computed(() => {
  if (systemStore.isHealthy) return '健康'
  if (systemStore.systemStatus) return '警告'
  return '错误'
})

const healthTooltip = computed(() => {
  if (systemStore.isHealthy) {
    return '系统运行正常'
  } else {
    return '系统存在问题，请检查系统状态页面'
  }
})

// WebSocket状态
const wsClass = computed(() => ({
  'ws-connected': wsStore.isConnected,
  'ws-disconnected': !wsStore.isConnected
}))

const wsText = computed(() => 
  wsStore.isConnected ? '实时连接' : '连接断开'
)

const wsTooltip = computed(() => 
  `WebSocket状态: ${wsStore.connectionStatus}`
)

// 内存状态
const memoryUsage = computed(() => systemStore.memoryUsage || 0)
const memoryColor = computed(() => systemStore.memoryUsageColor)

const memoryTooltip = computed(() => {
  const memory = systemStore.systemStatus?.memory
  if (!memory) return '内存信息不可用'
  
  const used = (memory.used / 1024 / 1024 / 1024).toFixed(2)
  const max = (memory.max / 1024 / 1024 / 1024).toFixed(2)
  
  return `内存使用: ${used}GB / ${max}GB (${memoryUsage.value.toFixed(1)}%)`
})

// 定时器
let timer: number | null = null

onMounted(() => {
  // 每秒更新时间
  timer = window.setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.app-footer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 左侧信息 */
.footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-text {
  white-space: nowrap;
}

/* 中间状态 */
.footer-center {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-regular);
}

.stat-item .el-icon {
  font-size: 14px;
}

/* 右侧状态 */
.footer-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 健康状态指示器 */
.health-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.health-good {
  color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.health-warning {
  color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.health-error {
  color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

/* WebSocket状态指示器 */
.ws-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
}

.ws-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.ws-connected {
  color: var(--el-color-success);
}

.ws-connected .ws-dot {
  background: var(--el-color-success);
  animation: pulse 2s infinite;
}

.ws-disconnected {
  color: var(--el-color-danger);
}

.ws-disconnected .ws-dot {
  background: var(--el-color-danger);
}

/* 内存指示器 */
.memory-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.memory-progress {
  width: 50px;
}

.memory-text {
  min-width: 35px;
  text-align: right;
  font-weight: 500;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式 */
@media (max-width: 1200px) {
  .footer-center {
    display: none;
  }
}

@media (max-width: 768px) {
  .app-footer {
    padding: 0 12px;
    font-size: 11px;
  }
  
  .footer-left {
    gap: 4px;
  }
  
  .footer-right {
    gap: 8px;
  }
  
  .memory-indicator {
    display: none;
  }
}

@media (max-width: 480px) {
  .footer-left .footer-text:nth-child(3),
  .footer-left .footer-text:nth-child(5) {
    display: none;
  }
  
  .footer-left .el-divider:nth-child(2),
  .footer-left .el-divider:nth-child(4) {
    display: none;
  }
}
</style>
