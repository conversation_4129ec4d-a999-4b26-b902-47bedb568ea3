<template>
  <div class="folder-chart">
    <div v-if="loading" class="chart-loading">
      <el-skeleton :rows="6" animated />
    </div>
    <div v-else class="chart-content">
      <!-- 收藏夹分布 -->
      <div class="chart-section">
        <h4 class="section-title">收藏夹分布</h4>
        <div class="distribution-chart">
          <div class="chart-item">
            <div class="chart-bar">
              <div 
                class="bar-fill valid" 
                :style="{ width: validPercentage + '%' }"
              ></div>
            </div>
            <div class="chart-label">
              <span class="label-text">有效视频</span>
              <span class="label-value">{{ validVideos }} ({{ validPercentage.toFixed(1) }}%)</span>
            </div>
          </div>
          
          <div class="chart-item">
            <div class="chart-bar">
              <div 
                class="bar-fill invalid" 
                :style="{ width: invalidPercentage + '%' }"
              ></div>
            </div>
            <div class="chart-label">
              <span class="label-text">失效视频</span>
              <span class="label-value">{{ invalidVideos }} ({{ invalidPercentage.toFixed(1) }}%)</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 监控状态 -->
      <div class="chart-section">
        <h4 class="section-title">监控状态</h4>
        <div class="status-grid">
          <div class="status-card monitoring">
            <div class="status-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ monitoringFolders }}</div>
              <div class="status-label">监控中</div>
            </div>
          </div>
          
          <div class="status-card preloading">
            <div class="status-icon">
              <el-icon><Download /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ preloadingFolders }}</div>
              <div class="status-label">预下载中</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="chart-section">
        <h4 class="section-title">最近活动</h4>
        <div class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="activity.type">
              <el-icon>
                <component :is="activity.icon" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-text">{{ activity.text }}</div>
              <div class="activity-time">{{ formatTime.relative(activity.time) }}</div>
            </div>
          </div>
          
          <div v-if="recentActivities.length === 0" class="no-activity">
            <el-empty description="暂无活动记录" :image-size="60" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  VideoPlay,
  Download,
  Plus,
  Refresh,
  Warning
} from '@element-plus/icons-vue'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { formatTime } from '@/utils'

// Props
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 状态管理
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()

// 计算属性
const totalVideos = computed(() => foldersStore.totalVideos)
const validVideos = computed(() => foldersStore.validVideos)
const invalidVideos = computed(() => foldersStore.invalidVideos)

const validPercentage = computed(() => {
  return totalVideos.value > 0 ? (validVideos.value / totalVideos.value) * 100 : 0
})

const invalidPercentage = computed(() => {
  return totalVideos.value > 0 ? (invalidVideos.value / totalVideos.value) * 100 : 0
})

const monitoringFolders = computed(() => monitorStore.monitoringCount)
const preloadingFolders = computed(() => preloadStore.preloadingCount)

// 模拟最近活动数据
const recentActivities = computed(() => [
  {
    id: 1,
    type: 'success',
    icon: Plus,
    text: '添加了新收藏夹',
    time: new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
  },
  {
    id: 2,
    type: 'info',
    icon: Refresh,
    text: '刷新了收藏夹数据',
    time: new Date(Date.now() - 15 * 60 * 1000) // 15分钟前
  },
  {
    id: 3,
    type: 'warning',
    icon: Warning,
    text: '发现失效视频',
    time: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
  }
])
</script>

<style scoped>
.folder-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-loading {
  height: 100%;
  padding: 20px;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.chart-section {
  flex-shrink: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

/* 分布图表 */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chart-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.chart-bar {
  height: 20px;
  background: var(--el-fill-color-lighter);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.bar-fill.valid {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.bar-fill.invalid {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.chart-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.label-text {
  color: var(--el-text-color-regular);
}

.label-value {
  color: var(--el-text-color-primary);
  font-weight: 600;
}

/* 状态网格 */
.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-card.monitoring .status-icon {
  background: #67c23a20;
  color: #67c23a;
}

.status-card.preloading .status-icon {
  background: #409eff20;
  color: #409eff;
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.status-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

/* 活动列表 */
.activity-list {
  max-height: 200px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.activity-icon.success {
  background: #67c23a20;
  color: #67c23a;
}

.activity-icon.info {
  background: #409eff20;
  color: #409eff;
}

.activity-icon.warning {
  background: #e6a23c20;
  color: #e6a23c;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 12px;
  color: var(--el-text-color-primary);
  line-height: 1.4;
}

.activity-time {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.no-activity {
  text-align: center;
  padding: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .status-card {
    padding: 10px;
  }
  
  .activity-item {
    gap: 8px;
  }
}
</style>
