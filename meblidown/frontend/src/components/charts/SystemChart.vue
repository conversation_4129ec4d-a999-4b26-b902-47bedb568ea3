<template>
  <div class="system-chart">
    <div v-if="loading" class="chart-loading">
      <el-skeleton :rows="6" animated />
    </div>
    <div v-else class="chart-content">
      <!-- 内存使用率 -->
      <div class="metric-item">
        <div class="metric-header">
          <el-icon><Cpu /></el-icon>
          <span>内存使用率</span>
        </div>
        <div class="metric-value">
          <el-progress
            :percentage="memoryUsage"
            :color="memoryColor"
            :stroke-width="8"
          />
          <span class="value-text">{{ memoryUsage.toFixed(1) }}%</span>
        </div>
      </div>

      <!-- 存储统计 -->
      <div class="metric-item">
        <div class="metric-header">
          <el-icon><FolderOpened /></el-icon>
          <span>存储统计</span>
        </div>
        <div class="storage-stats">
          <div class="stat-row">
            <span>收藏夹:</span>
            <span>{{ storageStats.folderCount || 0 }}</span>
          </div>
          <div class="stat-row">
            <span>视频:</span>
            <span>{{ storageStats.videoCount || 0 }}</span>
          </div>
          <div class="stat-row">
            <span>预下载:</span>
            <span>{{ storageStats.preloadCount || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 服务状态 -->
      <div class="metric-item">
        <div class="metric-header">
          <el-icon><Setting /></el-icon>
          <span>服务状态</span>
        </div>
        <div class="service-status">
          <div class="status-item">
            <div class="status-dot" :class="{ 'active': systemStore.isHealthy }"></div>
            <span>系统健康</span>
          </div>
          <div class="status-item">
            <div class="status-dot" :class="{ 'active': monitorStats.enabled }"></div>
            <span>监控服务</span>
          </div>
          <div class="status-item">
            <div class="status-dot" :class="{ 'active': wsStore.isConnected }"></div>
            <span>WebSocket</span>
          </div>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="metric-item">
        <div class="metric-header">
          <el-icon><TrendCharts /></el-icon>
          <span>实时数据</span>
        </div>
        <div class="realtime-data">
          <div class="data-row">
            <span>监控中:</span>
            <span class="data-value">{{ monitorStats.monitoringCount || 0 }}</span>
          </div>
          <div class="data-row">
            <span>预下载中:</span>
            <span class="data-value">{{ preloadingCount }}</span>
          </div>
          <div class="data-row">
            <span>WebSocket连接:</span>
            <span class="data-value">{{ websocketStats.activeConnections || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Cpu,
  FolderOpened,
  Setting,
  TrendCharts
} from '@element-plus/icons-vue'
import { useSystemStore } from '@/stores/system'
import { usePreloadStore } from '@/stores/preload'
import { useWebSocketStore } from '@/stores/websocket'

// Props
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 状态管理
const systemStore = useSystemStore()
const preloadStore = usePreloadStore()
const wsStore = useWebSocketStore()

// 计算属性
const memoryUsage = computed(() => systemStore.memoryUsage || 0)
const memoryColor = computed(() => systemStore.memoryUsageColor)
const storageStats = computed(() => systemStore.storageStats || {})
const monitorStats = computed(() => systemStore.monitorStats || {})
const websocketStats = computed(() => systemStore.websocketStats || {})
const preloadingCount = computed(() => preloadStore.preloadingCount || 0)
</script>

<style scoped>
.system-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-loading {
  height: 100%;
  padding: 20px;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  flex: 1;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.value-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.storage-stats,
.realtime-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-row,
.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.data-value {
  font-weight: 600;
  color: var(--el-color-primary);
}

.service-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--el-color-danger);
  transition: all 0.3s ease;
}

.status-dot.active {
  background: var(--el-color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .metric-item {
    padding: 12px;
  }
  
  .metric-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
