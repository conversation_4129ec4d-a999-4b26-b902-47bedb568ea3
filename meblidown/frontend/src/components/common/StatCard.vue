<template>
  <el-card class="stat-card" :class="{ 'loading': loading }" shadow="hover">
    <div class="stat-content">
      <!-- 图标区域 -->
      <div class="stat-icon" :style="{ backgroundColor: iconBgColor }">
        <el-icon :size="24" :color="color">
          <component :is="iconComponent" />
        </el-icon>
      </div>

      <!-- 数据区域 -->
      <div class="stat-data">
        <div class="stat-value" :style="{ color }">
          <el-skeleton-item v-if="loading" variant="text" style="width: 60px; height: 28px;" />
          <span v-else>{{ value }}</span>
        </div>
        <div class="stat-title">
          <el-skeleton-item v-if="loading" variant="text" style="width: 80px; height: 16px;" />
          <span v-else>{{ title }}</span>
        </div>
      </div>

      <!-- 趋势区域 -->
      <div v-if="trend && !loading" class="stat-trend" :class="trendClass">
        <el-icon :size="16">
          <component :is="trendIcon" />
        </el-icon>
        <span class="trend-value">{{ Math.abs(trend.value) }}%</span>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Folder,
  VideoPlay,
  View,
  Download,
  Cpu,
  SuccessFilled,
  Monitor,
  ArrowUp,
  ArrowDown,
  Loading
} from '@element-plus/icons-vue'

// Props
interface Props {
  title: string
  value: string | number
  icon: string
  color: string
  trend?: {
    value: number
    isUp: boolean
  }
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 图标映射
const iconMap = {
  Folder,
  VideoPlay,
  View,
  Download,
  Cpu,
  SuccessFilled,
  Monitor
}

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || Monitor
})

const iconBgColor = computed(() => {
  // 将颜色转换为浅色背景
  const color = props.color
  if (color.startsWith('#')) {
    return color + '20' // 添加透明度
  }
  return 'rgba(64, 158, 255, 0.1)' // 默认蓝色背景
})

const trendClass = computed(() => ({
  'trend-up': props.trend?.isUp,
  'trend-down': !props.trend?.isUp
}))

const trendIcon = computed(() => {
  return props.trend?.isUp ? ArrowUp : ArrowDown
})
</script>

<style scoped>
.stat-card {
  position: relative;
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.loading {
  pointer-events: none;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

/* 图标区域 */
.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

/* 数据区域 */
.stat-data {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.stat-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  line-height: 1.2;
}

/* 趋势区域 */
.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.trend-up {
  color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.trend-down {
  color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

.trend-value {
  font-size: 11px;
}

/* 加载遮罩 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loading-icon {
  animation: rotate 1s linear infinite;
  color: var(--el-color-primary);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .stat-content {
    gap: 12px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-title {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .stat-content {
    gap: 8px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-icon .el-icon {
    font-size: 20px !important;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .stat-title {
    font-size: 12px;
  }
  
  .stat-trend {
    display: none;
  }
}

/* 暗色主题适配 */
.dark .loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}
</style>
