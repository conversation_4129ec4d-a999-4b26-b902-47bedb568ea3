<template>
  <div class="quick-actions">
    <div class="actions-grid">
      <!-- 添加收藏夹 -->
      <div class="action-card" @click="showAddDialog">
        <div class="action-icon add">
          <el-icon><Plus /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">添加收藏夹</div>
          <div class="action-description">添加新的B站收藏夹</div>
        </div>
      </div>

      <!-- 批量启动监控 -->
      <div class="action-card" @click="startAllMonitors">
        <div class="action-icon monitor">
          <el-icon><VideoPlay /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">批量监控</div>
          <div class="action-description">启动所有收藏夹监控</div>
        </div>
      </div>

      <!-- 批量预下载 -->
      <div class="action-card" @click="startAllPreloads">
        <div class="action-icon preload">
          <el-icon><Download /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">批量预下载</div>
          <div class="action-description">启动所有预下载任务</div>
        </div>
      </div>

      <!-- 系统设置 -->
      <div class="action-card" @click="goToSettings">
        <div class="action-icon settings">
          <el-icon><Setting /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">系统设置</div>
          <div class="action-description">配置系统参数</div>
        </div>
      </div>

      <!-- 垃圾回收 -->
      <div class="action-card" @click="triggerGC">
        <div class="action-icon gc">
          <el-icon><Delete /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">垃圾回收</div>
          <div class="action-description">清理系统内存</div>
        </div>
      </div>

      <!-- 刷新数据 -->
      <div class="action-card" @click="refreshAllData">
        <div class="action-icon refresh">
          <el-icon><Refresh /></el-icon>
        </div>
        <div class="action-content">
          <div class="action-title">刷新数据</div>
          <div class="action-description">更新所有数据</div>
        </div>
      </div>
    </div>

    <!-- 系统状态快览 -->
    <div class="status-overview">
      <h4 class="overview-title">系统状态</h4>
      <div class="status-items">
        <div class="status-item">
          <div class="status-dot" :class="{ 'active': systemStore.isHealthy }"></div>
          <span class="status-text">系统健康</span>
        </div>
        <div class="status-item">
          <div class="status-dot" :class="{ 'active': wsStore.isConnected }"></div>
          <span class="status-text">WebSocket连接</span>
        </div>
        <div class="status-item">
          <div class="status-dot" :class="{ 'active': monitorStore.monitoringCount > 0 }"></div>
          <span class="status-text">监控服务</span>
        </div>
        <div class="status-item">
          <div class="status-dot" :class="{ 'active': preloadStore.preloadingCount > 0 }"></div>
          <span class="status-text">预下载服务</span>
        </div>
      </div>
    </div>

    <!-- 添加收藏夹对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加收藏夹"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="80px">
        <el-form-item label="收藏夹ID" prop="folderId">
          <el-input
            v-model="addForm.folderId"
            placeholder="请输入B站收藏夹ID"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAdd" :loading="adding">
          添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Plus,
  VideoPlay,
  Download,
  Setting,
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useSystemStore } from '@/stores/system'
import { useFoldersStore } from '@/stores/folders'
import { useMonitorStore } from '@/stores/monitor'
import { usePreloadStore } from '@/stores/preload'
import { useWebSocketStore } from '@/stores/websocket'

// 路由
const router = useRouter()

// 状态管理
const systemStore = useSystemStore()
const foldersStore = useFoldersStore()
const monitorStore = useMonitorStore()
const preloadStore = usePreloadStore()
const wsStore = useWebSocketStore()

// 响应式状态
const addDialogVisible = ref(false)
const adding = ref(false)
const addFormRef = ref<FormInstance>()

// 表单数据
const addForm = ref({
  folderId: ''
})

// 表单验证规则
const addRules: FormRules = {
  folderId: [
    { required: true, message: '请输入收藏夹ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '收藏夹ID必须是数字', trigger: 'blur' }
  ]
}

// 方法
const showAddDialog = () => {
  addForm.value.folderId = ''
  addDialogVisible.value = true
}

const handleAdd = async () => {
  if (!addFormRef.value) return
  
  try {
    await addFormRef.value.validate()
    adding.value = true
    
    const folderId = parseInt(addForm.value.folderId)
    await foldersStore.addFolder(folderId)
    
    ElMessage.success('收藏夹添加成功')
    addDialogVisible.value = false
    
  } catch (error: any) {
    if (error.fields) {
      return
    }
    ElMessage.error(error.message || '添加收藏夹失败')
  } finally {
    adding.value = false
  }
}

const startAllMonitors = async () => {
  try {
    const result = await monitorStore.startAllMonitors()
    ElMessage.success(`批量启动完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)
  } catch (error: any) {
    ElMessage.error(error.message || '批量启动失败')
  }
}

const startAllPreloads = async () => {
  try {
    const result = await preloadStore.startAllPreloads()
    ElMessage.success(`批量启动完成：成功 ${result.successCount} 个，失败 ${result.failCount} 个`)
  } catch (error: any) {
    ElMessage.error(error.message || '批量启动失败')
  }
}

const goToSettings = () => {
  router.push('/settings')
}

const triggerGC = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要执行垃圾回收吗？这可能会暂时影响系统性能。',
      '垃圾回收确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await systemStore.triggerGC()
    ElMessage.success('垃圾回收执行成功')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '垃圾回收执行失败')
    }
  }
}

const refreshAllData = async () => {
  try {
    await Promise.all([
      systemStore.fetchAllData(),
      foldersStore.fetchFolders(),
      monitorStore.fetchMonitorStatus(),
      preloadStore.fetchPreloadStatus()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}
</script>

<style scoped>
.quick-actions {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  flex: 1;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
}

.action-icon.add {
  background: #67c23a20;
  color: #67c23a;
}

.action-icon.monitor {
  background: #409eff20;
  color: #409eff;
}

.action-icon.preload {
  background: #e6a23c20;
  color: #e6a23c;
}

.action-icon.settings {
  background: #909399;
  color: white;
}

.action-icon.gc {
  background: #f56c6c20;
  color: #f56c6c;
}

.action-icon.refresh {
  background: #909399;
  color: white;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.action-description {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
}

/* 状态概览 */
.status-overview {
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 16px;
}

.overview-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--el-color-danger);
  transition: all 0.3s ease;
}

.status-dot.active {
  background: var(--el-color-success);
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .action-card {
    flex-direction: row;
    text-align: left;
    padding: 12px;
  }
  
  .action-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .action-title {
    font-size: 12px;
  }
  
  .action-description {
    font-size: 10px;
  }
}
</style>
