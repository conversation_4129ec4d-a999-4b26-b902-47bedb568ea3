<template>
  <div class="activity-list">
    <div v-if="loading" class="activity-loading">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else class="activity-content">
      <div 
        v-for="activity in activities" 
        :key="activity.id"
        class="activity-item"
        :class="activity.type"
      >
        <div class="activity-icon">
          <el-icon>
            <component :is="activity.icon" />
          </el-icon>
        </div>
        <div class="activity-details">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-description">{{ activity.description }}</div>
          <div class="activity-meta">
            <span class="activity-time">{{ formatTime.relative(activity.time) }}</span>
            <span v-if="activity.folder" class="activity-folder">
              收藏夹: {{ activity.folder }}
            </span>
          </div>
        </div>
        <div class="activity-status">
          <el-tag :type="getTagType(activity.type)" size="small">
            {{ getStatusText(activity.type) }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="activities.length === 0" class="no-activity">
        <el-empty description="暂无活动记录" :image-size="80">
          <el-button type="primary" @click="refreshActivities">
            刷新活动
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Plus,
  Refresh,
  VideoPlay,
  VideoPause,
  Download,
  Warning,
  SuccessFilled,
  CircleCloseFilled,
  Delete
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'

// Props
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 活动类型定义
interface Activity {
  id: number
  type: 'success' | 'info' | 'warning' | 'error'
  icon: any
  title: string
  description: string
  time: Date
  folder?: string
}

// 响应式状态
const activities = ref<Activity[]>([])

// 计算属性
const getTagType = (type: string) => {
  switch (type) {
    case 'success': return 'success'
    case 'info': return 'primary'
    case 'warning': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (type: string) => {
  switch (type) {
    case 'success': return '成功'
    case 'info': return '信息'
    case 'warning': return '警告'
    case 'error': return '错误'
    default: return '未知'
  }
}

// 方法
const generateMockActivities = (): Activity[] => {
  const now = new Date()
  
  return [
    {
      id: 1,
      type: 'success',
      icon: Plus,
      title: '添加收藏夹',
      description: '成功添加新的收藏夹到监控列表',
      time: new Date(now.getTime() - 2 * 60 * 1000), // 2分钟前
      folder: '我的收藏'
    },
    {
      id: 2,
      type: 'info',
      icon: VideoPlay,
      title: '启动监控',
      description: '开始监控收藏夹视频变化',
      time: new Date(now.getTime() - 5 * 60 * 1000), // 5分钟前
      folder: '学习资料'
    },
    {
      id: 3,
      type: 'success',
      icon: Download,
      title: '预下载完成',
      description: '成功预下载了15个视频的CDN链接',
      time: new Date(now.getTime() - 8 * 60 * 1000), // 8分钟前
      folder: '技术分享'
    },
    {
      id: 4,
      type: 'warning',
      icon: Warning,
      title: '发现失效视频',
      description: '检测到3个视频已失效，需要处理',
      time: new Date(now.getTime() - 12 * 60 * 1000), // 12分钟前
      folder: '电影收藏'
    },
    {
      id: 5,
      type: 'info',
      icon: Refresh,
      title: '刷新数据',
      description: '手动刷新了收藏夹数据',
      time: new Date(now.getTime() - 15 * 60 * 1000), // 15分钟前
    },
    {
      id: 6,
      type: 'error',
      icon: CircleCloseFilled,
      title: '监控失败',
      description: '监控任务执行失败，请检查网络连接',
      time: new Date(now.getTime() - 20 * 60 * 1000), // 20分钟前
      folder: '音乐收藏'
    },
    {
      id: 7,
      type: 'success',
      icon: SuccessFilled,
      title: '系统启动',
      description: '系统成功启动，所有服务运行正常',
      time: new Date(now.getTime() - 25 * 60 * 1000), // 25分钟前
    },
    {
      id: 8,
      type: 'info',
      icon: VideoPause,
      title: '停止监控',
      description: '手动停止了收藏夹监控任务',
      time: new Date(now.getTime() - 30 * 60 * 1000), // 30分钟前
      folder: '游戏视频'
    }
  ]
}

const refreshActivities = () => {
  activities.value = generateMockActivities()
}

// 生命周期
onMounted(() => {
  refreshActivities()
})
</script>

<style scoped>
.activity-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-loading {
  padding: 20px;
}

.activity-content {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: var(--el-fill-color-lighter);
  margin: 0 -12px;
  padding: 16px 12px;
  border-radius: 8px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.activity-item.success .activity-icon {
  background: #67c23a20;
  color: #67c23a;
}

.activity-item.info .activity-icon {
  background: #409eff20;
  color: #409eff;
}

.activity-item.warning .activity-icon {
  background: #e6a23c20;
  color: #e6a23c;
}

.activity-item.error .activity-icon {
  background: #f56c6c20;
  color: #f56c6c;
}

.activity-details {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  margin-bottom: 6px;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.activity-time {
  font-weight: 500;
}

.activity-folder {
  padding: 2px 6px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  font-size: 11px;
}

.activity-status {
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 2px;
}

.no-activity {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 滚动条样式 */
.activity-content::-webkit-scrollbar {
  width: 4px;
}

.activity-content::-webkit-scrollbar-track {
  background: transparent;
}

.activity-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 2px;
}

.activity-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式 */
@media (max-width: 768px) {
  .activity-item {
    gap: 8px;
    padding: 12px 0;
  }
  
  .activity-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .activity-title {
    font-size: 13px;
  }
  
  .activity-description {
    font-size: 12px;
  }
  
  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
