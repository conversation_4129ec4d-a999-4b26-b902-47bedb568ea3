/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityList: typeof import('./src/components/dashboard/ActivityList.vue')['default']
    AppFooter: typeof import('./src/components/layout/AppFooter.vue')['default']
    AppHeader: typeof import('./src/components/layout/AppHeader.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    AppSidebar: typeof import('./src/components/layout/AppSidebar.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElLoading: typeof import('element-plus/es')['ElLoading']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FolderChart: typeof import('./src/components/charts/FolderChart.vue')['default']
    QuickActions: typeof import('./src/components/dashboard/QuickActions.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatCard: typeof import('./src/components/common/StatCard.vue')['default']
    SystemChart: typeof import('./src/components/charts/SystemChart.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
