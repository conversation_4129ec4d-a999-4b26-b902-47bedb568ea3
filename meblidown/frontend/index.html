<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="MeBiliDown - 现代化B站收藏夹监控与预下载系统" />
    <meta name="keywords" content="bilibili,收藏夹,监控,预下载,vue3,typescript" />
    <meta name="author" content="MeBiliDown" />
    
    <!-- Open Graph -->
    <meta property="og:title" content="MeBiliDown" />
    <meta property="og:description" content="现代化B站收藏夹监控与预下载系统" />
    <meta property="og:type" content="website" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="http://localhost:8080" />
    
    <title>MeBiliDown</title>
    
    <!-- 内联关键CSS -->
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #409eff, #67c23a);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 32px;
        font-weight: bold;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        color: #606266;
        font-size: 16px;
        margin-bottom: 20px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-tips {
        color: #909399;
        font-size: 14px;
        margin-top: 20px;
        text-align: center;
        max-width: 300px;
        line-height: 1.5;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 暗色主题 */
      @media (prefers-color-scheme: dark) {
        #loading {
          background: #1d1e1f;
        }
        .loading-text {
          color: #c0c4cc;
        }
        .loading-tips {
          color: #909399;
        }
      }
    </style>
  </head>
  <body>
    <!-- 应用容器 -->
    <div id="app">
      <!-- 加载动画 -->
      <div id="loading">
        <div class="loading-logo">M</div>
        <div class="loading-text">MeBiliDown</div>
        <div class="loading-spinner"></div>
        <div class="loading-tips">
          正在启动现代化B站收藏夹监控与预下载系统...<br>
          首次加载可能需要几秒钟时间
        </div>
      </div>
    </div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 移除加载动画 -->
    <script>
      // 当应用加载完成后移除加载动画
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
              loading.remove();
            }, 500);
          }
        }, 1000); // 至少显示1秒
      });
      
      // 错误处理 - 更宽容的错误处理
      let errorCount = 0;
      window.addEventListener('error', (event) => {
        errorCount++;
        console.error('应用加载错误:', event.error);

        // 只有在多个错误或严重错误时才显示错误信息
        if (errorCount > 2 || (event.error && event.error.message && event.error.message.includes('Failed to fetch'))) {
          const loading = document.getElementById('loading');
          if (loading) {
            const tips = loading.querySelector('.loading-tips');
            if (tips) {
              tips.innerHTML = '应用加载失败，请刷新页面重试<br>如果问题持续存在，请检查网络连接';
              tips.style.color = '#f56c6c';
            }
          }
        }
      });

      // 超时处理 - 延长超时时间
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading && loading.style.opacity !== '0') {
          const tips = loading.querySelector('.loading-tips');
          if (tips && !tips.innerHTML.includes('失败')) {
            tips.innerHTML = '加载时间较长，请稍候...<br>正在初始化应用组件';
            tips.style.color = '#e6a23c';
          }
        }
      }, 15000); // 延长到15秒

      // 强制移除加载动画 - 防止卡住
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading && loading.style.opacity !== '0') {
          console.log('强制移除加载动画');
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.5s ease';
          setTimeout(() => {
            loading.remove();
          }, 500);
        }
      }, 20000); // 20秒后强制移除
    </script>
  </body>
</html>
