<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - MeBiliDown</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f7fa;
        }
        .log {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #409eff;
        }
        .error {
            border-left-color: #f56c6c;
            background: #fef0f0;
        }
        .success {
            border-left-color: #67c23a;
            background: #f0f9ff;
        }
    </style>
</head>
<body>
    <h1>MeBiliDown Debug 页面</h1>
    <div id="logs"></div>

    <script>
        const logs = document.getElementById('logs');
        
        function addLog(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            console.log(message);
        }

        addLog('Debug页面加载成功', 'success');
        
        // 测试基本的JavaScript功能
        try {
            addLog('JavaScript基本功能正常', 'success');
            
            // 测试ES6模块支持
            const testArrow = () => 'ES6 Arrow Function';
            addLog(`ES6支持测试: ${testArrow()}`, 'success');
            
            // 测试Promise支持
            Promise.resolve('Promise支持正常').then(msg => {
                addLog(msg, 'success');
            });
            
            // 测试fetch API
            addLog('开始测试API连接...');
            fetch('/api/system/info')
                .then(response => {
                    addLog(`API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                    return response.json();
                })
                .then(data => {
                    addLog(`API数据获取成功: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
                })
                .catch(error => {
                    addLog(`API连接失败: ${error.message}`, 'error');
                });
                
        } catch (error) {
            addLog(`JavaScript错误: ${error.message}`, 'error');
        }
        
        // 监听全局错误
        window.addEventListener('error', (event) => {
            addLog(`全局错误: ${event.error?.message || event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
