<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeBiliDown - 连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .test-item {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background: #67c23a; color: white; }
        .error { background: #f56c6c; color: white; }
        .loading { background: #409eff; color: white; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>MeBiliDown 连接测试</h1>
    
    <div class="test-item">
        <h3>1. 前端服务状态</h3>
        <p>状态: <span id="frontend-status" class="status loading">检测中...</span></p>
        <p>当前页面能正常加载，说明前端服务运行正常</p>
    </div>

    <div class="test-item">
        <h3>2. 后端API连接测试</h3>
        <p>状态: <span id="api-status" class="status loading">检测中...</span></p>
        <p>测试地址: <code>/api/system/info</code></p>
        <pre id="api-response">等待响应...</pre>
    </div>

    <div class="test-item">
        <h3>3. 收藏夹API测试</h3>
        <p>状态: <span id="folders-status" class="status loading">检测中...</span></p>
        <p>测试地址: <code>/api/folders</code></p>
        <pre id="folders-response">等待响应...</pre>
    </div>

    <div class="test-item">
        <h3>4. WebSocket连接测试</h3>
        <p>状态: <span id="ws-status" class="status loading">检测中...</span></p>
        <p>测试地址: <code>ws://localhost:3000/ws</code></p>
        <pre id="ws-response">等待连接...</pre>
    </div>

    <script>
        // 更新状态
        function updateStatus(id, status, text) {
            const element = document.getElementById(id);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        // 测试API连接
        async function testAPI(url, statusId, responseId) {
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus(statusId, 'success', '连接成功');
                    document.getElementById(responseId).textContent = JSON.stringify(data, null, 2);
                } else {
                    updateStatus(statusId, 'error', `HTTP ${response.status}`);
                    document.getElementById(responseId).textContent = `错误: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                updateStatus(statusId, 'error', '连接失败');
                document.getElementById(responseId).textContent = `错误: ${error.message}`;
            }
        }

        // 测试WebSocket连接
        function testWebSocket() {
            try {
                const ws = new WebSocket('ws://localhost:3000/ws');
                
                ws.onopen = () => {
                    updateStatus('ws-status', 'success', '连接成功');
                    document.getElementById('ws-response').textContent = 'WebSocket连接已建立';
                    ws.close();
                };
                
                ws.onerror = (error) => {
                    updateStatus('ws-status', 'error', '连接失败');
                    document.getElementById('ws-response').textContent = `WebSocket错误: ${error}`;
                };
                
                ws.onclose = (event) => {
                    if (event.code !== 1000) {
                        updateStatus('ws-status', 'error', '连接关闭');
                        document.getElementById('ws-response').textContent = `连接关闭: ${event.code} ${event.reason}`;
                    }
                };
                
                // 超时处理
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        updateStatus('ws-status', 'error', '连接超时');
                        document.getElementById('ws-response').textContent = 'WebSocket连接超时';
                    }
                }, 5000);
                
            } catch (error) {
                updateStatus('ws-status', 'error', '连接失败');
                document.getElementById('ws-response').textContent = `WebSocket错误: ${error.message}`;
            }
        }

        // 开始测试
        window.addEventListener('load', () => {
            // 前端服务状态
            updateStatus('frontend-status', 'success', '运行正常');
            
            // 测试API
            testAPI('/api/system/info', 'api-status', 'api-response');
            testAPI('/api/folders', 'folders-status', 'folders-response');
            
            // 测试WebSocket
            testWebSocket();
        });
    </script>
</body>
</html>
