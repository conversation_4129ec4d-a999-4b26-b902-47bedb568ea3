# 🚀 IntelliJ IDEA 项目启动指南

## 📋 前置要求

### 必需软件
- **IntelliJ IDEA** 2023.1+ (推荐Ultimate版本)
- **Java 17+** (Spring Boot 3.x要求)
- **Node.js 16+** (前端开发)
- **Maven 3.6+** (后端构建)

### IDEA插件
确保安装以下插件：
- **Spring Boot** (通常已内置)
- **Vue.js** 
- **TypeScript**
- **JavaScript and TypeScript**
- **Node.js**

## 🔧 项目导入步骤

### 1. 打开项目
```
File → Open → 选择 meblidown 文件夹 → OK
```

### 2. 配置JDK
```
File → Project Structure → Project Settings → Project
- Project SDK: 选择 Java 17+
- Project language level: 17
```

### 3. 配置Maven
```
File → Settings → Build Tools → Maven
- Maven home path: 确保指向正确的Maven安装路径
- User settings file: 使用默认或自定义settings.xml
```

### 4. 配置Node.js
```
File → Settings → Languages & Frameworks → Node.js
- Node interpreter: 选择已安装的Node.js路径
- Package manager: npm (默认)
```

## 🚀 启动方式

### 方式一：使用预配置的运行配置

#### 1. 开发环境 (推荐)
在IDEA右上角运行配置下拉菜单中选择：
- **Full Stack (Development)** - 同时启动模拟后端和前端

#### 2. 分别启动
- **Mock Server** - 启动模拟后端服务 (端口8080)
- **Frontend (Dev Server)** - 启动前端开发服务器 (端口3000)
- **Backend (Spring Boot)** - 启动真实Spring Boot后端 (需要Java 17+)

### 方式二：手动启动

#### 启动模拟后端
1. 打开终端 (Alt + F12)
2. 执行命令：
```bash
cd meblidown
node mock-server.js
```

#### 启动前端
1. 打开新终端
2. 执行命令：
```bash
cd meblidown/frontend
npm run dev
```

#### 启动真实后端 (可选)
1. 确保Java版本为17+
2. 打开新终端
3. 执行命令：
```bash
cd meblidown/backend
mvn spring-boot:run
```

## 🌐 访问地址

启动成功后，可以访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080/api
- **API文档**: http://localhost:8080/swagger-ui.html (真实后端)
- **WebSocket**: ws://localhost:8080/ws

## 🔍 调试配置

### 前端调试
1. 在IDEA中设置断点
2. 启动 "Frontend (Dev Server)" 配置
3. 在浏览器中打开开发者工具
4. 使用Vue DevTools扩展

### 后端调试
1. 在Java代码中设置断点
2. 以Debug模式启动 "Backend (Spring Boot)" 配置
3. 发送API请求触发断点

### 模拟服务器调试
1. 在mock-server.js中设置断点
2. 以Debug模式启动 "Mock Server" 配置

## 📁 项目结构

```
meblidown/
├── backend/                 # Spring Boot后端
│   ├── src/main/java/      # Java源码
│   ├── src/main/resources/ # 配置文件
│   └── pom.xml             # Maven配置
├── frontend/               # Vue.js前端
│   ├── src/                # 前端源码
│   ├── public/             # 静态资源
│   ├── package.json        # npm配置
│   └── vite.config.ts      # Vite配置
├── mock-server.js          # 模拟后端服务
├── start.bat              # Windows启动脚本
├── start.sh               # Linux/Mac启动脚本
└── stop.sh                # 停止脚本
```

## 🛠️ 常见问题

### Java版本问题
如果遇到Java版本错误：
1. 确保安装了Java 17+
2. 在IDEA中配置正确的JDK版本
3. 检查JAVA_HOME环境变量

### 端口占用问题
如果端口被占用：
1. 修改application.yml中的server.port
2. 或者杀死占用端口的进程

### 前端依赖问题
如果前端启动失败：
1. 删除node_modules文件夹
2. 重新运行 `npm install`
3. 确保Node.js版本为16+

### Maven依赖问题
如果后端依赖下载失败：
1. 检查网络连接
2. 配置Maven镜像源
3. 清理Maven缓存：`mvn clean`

## 💡 开发提示

### 热重载
- **前端**: 修改代码自动刷新浏览器
- **后端**: 使用Spring Boot DevTools实现热重载

### 代码格式化
- 使用IDEA的代码格式化功能 (Ctrl + Alt + L)
- 配置ESLint和Prettier for前端代码

### Git集成
- IDEA内置Git支持
- 使用VCS菜单进行版本控制操作

### 数据库连接
- 在application.yml中配置数据库连接
- 使用IDEA的Database工具窗口管理数据库

## 🎯 下一步

1. **熟悉项目结构** - 浏览代码了解架构
2. **测试功能** - 在浏览器中测试各个功能
3. **查看API文档** - 了解后端接口设计
4. **开始开发** - 根据需求添加新功能

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查IDEA的Event Log
3. 查看项目README.md文档
4. 参考Spring Boot和Vue.js官方文档
